{% extends "base.html" %}

{% block title %}إضافة موظف جديد - برنامج تسيير مستخدمي الجمارك الجزائرية{% endblock %}

{% block page_title %}إضافة موظف جديد{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item">إدارة الموظفين</li>
<li class="breadcrumb-item active">إضافة موظف جديد</li>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css">
<style>
    .form-section {
        background: #f8f9fa;
        border-left: 4px solid #007bff;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }
    
    .form-section h5 {
        color: #007bff;
        margin-bottom: 15px;
        font-weight: bold;
    }
    
    .required {
        color: #dc3545;
    }
    
    .photo-preview {
        max-width: 200px;
        max-height: 250px;
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 10px;
        text-align: center;
        background: #f8f9fa;
    }
    
    .photo-preview img {
        max-width: 100%;
        max-height: 200px;
        border-radius: 4px;
    }
    
    .photo-upload-area {
        border: 2px dashed #007bff;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        background: #f8f9fa;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .photo-upload-area:hover {
        background: #e9ecef;
        border-color: #0056b3;
    }
    
    .photo-upload-area.dragover {
        background: #cce7ff;
        border-color: #0056b3;
    }
</style>
{% endblock %}

{% block content %}
<form method="POST" enctype="multipart/form-data" id="employeeForm">
    {{ form.hidden_tag() }}
    
    <div class="row">
        <div class="col-md-8">
            <!-- البيانات الأساسية -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user"></i> البيانات الأساسية
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.registration_number.label(class="form-label") }} <span class="required">*</span>
                                {{ form.registration_number(class="form-control") }}
                                {% if form.registration_number.errors %}
                                    <div class="text-danger">
                                        {% for error in form.registration_number.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.gender.label(class="form-label") }} <span class="required">*</span>
                                {{ form.gender(class="form-control") }}
                                {% if form.gender.errors %}
                                    <div class="text-danger">
                                        {% for error in form.gender.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.last_name.label(class="form-label") }} <span class="required">*</span>
                                {{ form.last_name(class="form-control") }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.last_name.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.first_name.label(class="form-label") }} <span class="required">*</span>
                                {{ form.first_name(class="form-control") }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.first_name.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.nom_fr.label(class="form-label") }}
                                {{ form.nom_fr(class="form-control") }}
                                {% if form.nom_fr.errors %}
                                    <div class="text-danger">
                                        {% for error in form.nom_fr.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.prenom_fr.label(class="form-label") }}
                                {{ form.prenom_fr(class="form-control") }}
                                {% if form.prenom_fr.errors %}
                                    <div class="text-danger">
                                        {% for error in form.prenom_fr.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.birth_date.label(class="form-label") }} <span class="required">*</span>
                                {{ form.birth_date(class="form-control datepicker") }}
                                {% if form.birth_date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.birth_date.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.birth_wilaya_id.label(class="form-label") }} <span class="required">*</span>
                                {{ form.birth_wilaya_id(class="form-control", id="birth_wilaya") }}
                                {% if form.birth_wilaya_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.birth_wilaya_id.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.birth_commune_id.label(class="form-label") }} <span class="required">*</span>
                                {{ form.birth_commune_id(class="form-control", id="birth_commune") }}
                                {% if form.birth_commune_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.birth_commune_id.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.marital_status.label(class="form-label") }} <span class="required">*</span>
                                {{ form.marital_status(class="form-control", id="marital_status") }}
                                {% if form.marital_status.errors %}
                                    <div class="text-danger">
                                        {% for error in form.marital_status.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4" id="children_count_div">
                            <div class="form-group">
                                {{ form.children_count.label(class="form-label") }}
                                {{ form.children_count(class="form-control") }}
                                {% if form.children_count.errors %}
                                    <div class="text-danger">
                                        {% for error in form.children_count.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.dependents_count.label(class="form-label") }}
                                {{ form.dependents_count(class="form-control") }}
                                {% if form.dependents_count.errors %}
                                    <div class="text-danger">
                                        {% for error in form.dependents_count.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.blood_type.label(class="form-label") }}
                                {{ form.blood_type(class="form-control") }}
                                {% if form.blood_type.errors %}
                                    <div class="text-danger">
                                        {% for error in form.blood_type.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- بيانات الاتصال -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-phone"></i> بيانات الاتصال
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.phone1.label(class="form-label") }}
                                {{ form.phone1(class="form-control") }}
                                {% if form.phone1.errors %}
                                    <div class="text-danger">
                                        {% for error in form.phone1.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.phone2.label(class="form-label") }}
                                {{ form.phone2(class="form-control") }}
                                {% if form.phone2.errors %}
                                    <div class="text-danger">
                                        {% for error in form.phone2.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.email.label(class="form-label") }}
                        {{ form.email(class="form-control") }}
                        {% if form.email.errors %}
                            <div class="text-danger">
                                {% for error in form.email.errors %}
                                    <small>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.primary_address.label(class="form-label") }}
                        {{ form.primary_address(class="form-control", rows="3") }}
                        {% if form.primary_address.errors %}
                            <div class="text-danger">
                                {% for error in form.primary_address.errors %}
                                    <small>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.secondary_address.label(class="form-label") }}
                        {{ form.secondary_address(class="form-control", rows="3") }}
                        {% if form.secondary_address.errors %}
                            <div class="text-danger">
                                {% for error in form.secondary_address.errors %}
                                    <small>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.emergency_contact_name.label(class="form-label") }}
                        {{ form.emergency_contact_name(class="form-control") }}
                        {% if form.emergency_contact_name.errors %}
                            <div class="text-danger">
                                {% for error in form.emergency_contact_name.errors %}
                                    <small>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.emergency_contact_address.label(class="form-label") }}
                        {{ form.emergency_contact_address(class="form-control", rows="3") }}
                        {% if form.emergency_contact_address.errors %}
                            <div class="text-danger">
                                {% for error in form.emergency_contact_address.errors %}
                                    <small>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- صورة الموظف -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-camera"></i> صورة الموظف
                    </h3>
                </div>
                <div class="card-body">
                    <div class="photo-upload-area" onclick="document.getElementById('photo').click();">
                        <div id="photo-preview" class="photo-preview">
                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                            <p class="text-muted">انقر لاختيار صورة أو اسحب الصورة هنا</p>
                            <small class="text-muted">الحد الأقصى: 5MB<br>الصيغ المدعومة: JPG, PNG, GIF</small>
                        </div>
                    </div>
                    {{ form.photo(style="display: none;", id="photo") }}
                    {% if form.photo.errors %}
                        <div class="text-danger mt-2">
                            {% for error in form.photo.errors %}
                                <small>{{ error }}</small><br>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- أزرار الحفظ -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary btn-lg") }}
                        <a href="{{ url_for('employees_list') }}" class="btn btn-secondary">إلغاء</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/locales/bootstrap-datepicker.ar.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize datepicker
    $('.datepicker').datepicker({
        format: 'yyyy-mm-dd',
        language: 'ar',
        autoclose: true,
        todayHighlight: true,
        orientation: 'bottom auto'
    });
    
    // Handle marital status change
    $('#marital_status').change(function() {
        if ($(this).val() === 'أعزب') {
            $('#children_count_div').hide();
            $('#children_count').val(0);
        } else {
            $('#children_count_div').show();
        }
    });
    
    // Trigger on page load
    $('#marital_status').trigger('change');
    
    // Handle photo upload
    $('#photo').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#photo-preview').html('<img src="' + e.target.result + '" alt="صورة الموظف">');
            };
            reader.readAsDataURL(file);
        }
    });
    
    // Handle drag and drop for photo
    const photoUploadArea = $('.photo-upload-area')[0];
    
    photoUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });
    
    photoUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });
    
    photoUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (file.type.startsWith('image/')) {
                document.getElementById('photo').files = files;
                $('#photo').trigger('change');
            }
        }
    });
    
    // Handle wilaya change to update communes
    $('#birth_wilaya').change(function() {
        const wilayaId = $(this).val();
        if (wilayaId) {
            // This would typically make an AJAX call to get communes
            // For now, we'll just clear the commune dropdown
            $('#birth_commune').html('<option value="">اختر البلدية...</option>');
        }
    });
});
</script>
{% endblock %}

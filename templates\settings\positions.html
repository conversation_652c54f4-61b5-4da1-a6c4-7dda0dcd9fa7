{% extends "base.html" %}

{% block title %}الوظائف والمصالح - الإعدادات{% endblock %}

{% block page_title %}الوظائف والمصالح{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item"><a href="{{ url_for('settings') }}">الإعدادات</a></li>
<li class="breadcrumb-item active">الوظائف والمصالح</li>
{% endblock %}

{% block content %}
<div class="row">
    <!-- إدارة المديريات -->
    <div class="col-md-4">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-building"></i> المديريات
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#addDirectorateModal">
                        <i class="fas fa-plus"></i> إضافة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="list-group" id="directoratesList">
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">المديرية الجهوية للجمارك بالأغواط</h6>
                            <div>
                                <button class="btn btn-warning btn-xs" onclick="editDirectorate(1)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-danger btn-xs" onclick="deleteDirectorate(1)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <p class="mb-1"><small class="text-muted">مديرية جهوية</small></p>
                        <small class="text-success">3 مديريات فرعية، 4 مفتشيات</small>
                    </div>
                    
                    <div class="list-group-item ml-3">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">المديرية الفرعية لإدارة الوسائل</h6>
                            <div>
                                <button class="btn btn-warning btn-xs" onclick="editDirectorate(2)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-danger btn-xs" onclick="deleteDirectorate(2)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <p class="mb-1"><small class="text-muted">مديرية فرعية</small></p>
                        <small class="text-info">3 مكاتب</small>
                    </div>
                    
                    <div class="list-group-item ml-3">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">المديرية الفرعية للمنازعات الجمركية والتحصيل</h6>
                            <div>
                                <button class="btn btn-warning btn-xs" onclick="editDirectorate(3)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-danger btn-xs" onclick="deleteDirectorate(3)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <p class="mb-1"><small class="text-muted">مديرية فرعية</small></p>
                        <small class="text-info">2 مكاتب</small>
                    </div>
                    
                    <div class="list-group-item ml-3">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">مفتشية أقسام الجمارك بالأغواط</h6>
                            <div>
                                <button class="btn btn-warning btn-xs" onclick="editDirectorate(4)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-danger btn-xs" onclick="deleteDirectorate(4)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <p class="mb-1"><small class="text-muted">مفتشية</small></p>
                        <small class="text-warning">5 مكاتب، 3 فرق</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إدارة المصالح -->
    <div class="col-md-4">
        <div class="card card-success">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-sitemap"></i> المصالح
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#addServiceModal">
                        <i class="fas fa-plus"></i> إضافة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- فلتر حسب المديرية -->
                <div class="form-group">
                    <label for="directorateFilter">فلترة حسب المديرية:</label>
                    <select class="form-control form-control-sm" id="directorateFilter" onchange="filterServices()">
                        <option value="">جميع المديريات</option>
                        <option value="1">المديرية الفرعية لإدارة الوسائل</option>
                        <option value="2">المديرية الفرعية للإعلام الآلي والاتصال</option>
                        <option value="3">مفتشية أقسام الجمارك</option>
                    </select>
                </div>

                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المصلحة</th>
                                <th>المديرية</th>
                                <th>الوظائف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="servicesTableBody">
                            <tr data-directorate="1">
                                <td>مكتب تسيير المستخدمين والتكوين</td>
                                <td><small class="text-muted">إدارة الوسائل</small></td>
                                <td><span class="badge badge-info">4</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editService(1)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deleteService(1)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr data-directorate="1">
                                <td>مكتب الوسائل الإمدادية</td>
                                <td><small class="text-muted">إدارة الوسائل</small></td>
                                <td><span class="badge badge-info">3</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editService(2)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deleteService(2)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr data-directorate="2">
                                <td>مكتب الإعلام الآلي</td>
                                <td><small class="text-muted">الإعلام الآلي والاتصال</small></td>
                                <td><span class="badge badge-info">4</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editService(3)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deleteService(3)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr data-directorate="3">
                                <td>قباضة الجمارك بالأغواط</td>
                                <td><small class="text-muted">مفتشية أقسام الجمارك</small></td>
                                <td><span class="badge badge-info">5</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editService(4)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deleteService(4)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- إدارة الوظائف -->
    <div class="col-md-4">
        <div class="card card-warning">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-users-cog"></i> الوظائف
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#addPositionModal">
                        <i class="fas fa-plus"></i> إضافة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- فلتر حسب المصلحة -->
                <div class="form-group">
                    <label for="serviceFilter">فلترة حسب المصلحة:</label>
                    <select class="form-control form-control-sm" id="serviceFilter" onchange="filterPositions()">
                        <option value="">جميع المصالح</option>
                        <option value="1">مكتب تسيير المستخدمين والتكوين</option>
                        <option value="2">مكتب الوسائل الإمدادية</option>
                        <option value="3">مكتب الإعلام الآلي</option>
                        <option value="4">قباضة الجمارك بالأغواط</option>
                    </select>
                </div>

                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الوظيفة</th>
                                <th>المصلحة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="positionsTableBody">
                            <tr data-service="1">
                                <td>رئيس مكتب تسيير المستخدمين والتكوين</td>
                                <td><small class="text-muted">تسيير المستخدمين</small></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editPosition(1)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deletePosition(1)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr data-service="1">
                                <td>مكلف بالموارد البشرية</td>
                                <td><small class="text-muted">تسيير المستخدمين</small></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editPosition(2)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deletePosition(2)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr data-service="3">
                                <td>رئيس مكتب الإعلام الآلي</td>
                                <td><small class="text-muted">الإعلام الآلي</small></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editPosition(3)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deletePosition(3)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr data-service="3">
                                <td>مهندس إعلام آلي</td>
                                <td><small class="text-muted">الإعلام الآلي</small></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editPosition(4)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deletePosition(4)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr data-service="4">
                                <td>رئيس قباضة الجمارك</td>
                                <td><small class="text-muted">قباضة الجمارك</small></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editPosition(5)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deletePosition(5)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Directorate Modal -->
<div class="modal fade" id="addDirectorateModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مديرية جديدة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="addDirectorateForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="directorateName">اسم المديرية</label>
                        <input type="text" class="form-control" id="directorateName" required>
                    </div>
                    <div class="form-group">
                        <label for="directorateType">نوع المديرية</label>
                        <select class="form-control" id="directorateType" required>
                            <option value="">اختر النوع</option>
                            <option value="مديرية جهوية">مديرية جهوية</option>
                            <option value="مديرية فرعية">مديرية فرعية</option>
                            <option value="مفتشية">مفتشية</option>
                            <option value="مفتشية رئيسية">مفتشية رئيسية</option>
                            <option value="مكتب">مكتب</option>
                            <option value="فرقة">فرقة</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="parentDirectorate">المديرية الأم</label>
                        <select class="form-control" id="parentDirectorate">
                            <option value="">لا توجد (مديرية رئيسية)</option>
                            <option value="1">المديرية الجهوية للجمارك بالأغواط</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Service Modal -->
<div class="modal fade" id="addServiceModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مصلحة جديدة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="addServiceForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="serviceName">اسم المصلحة</label>
                        <input type="text" class="form-control" id="serviceName" required>
                    </div>
                    <div class="form-group">
                        <label for="serviceDirectorate">المديرية</label>
                        <select class="form-control" id="serviceDirectorate" required>
                            <option value="">اختر المديرية</option>
                            <option value="1">المديرية الفرعية لإدارة الوسائل</option>
                            <option value="2">المديرية الفرعية للإعلام الآلي والاتصال</option>
                            <option value="3">مفتشية أقسام الجمارك بالأغواط</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Position Modal -->
<div class="modal fade" id="addPositionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة وظيفة جديدة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="addPositionForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="positionName">اسم الوظيفة</label>
                        <input type="text" class="form-control" id="positionName" required>
                    </div>
                    <div class="form-group">
                        <label for="positionService">المصلحة</label>
                        <select class="form-control" id="positionService" required>
                            <option value="">اختر المصلحة</option>
                            <option value="1">مكتب تسيير المستخدمين والتكوين</option>
                            <option value="2">مكتب الوسائل الإمدادية</option>
                            <option value="3">مكتب الإعلام الآلي</option>
                            <option value="4">قباضة الجمارك بالأغواط</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Filter functions
function filterServices() {
    var filter = document.getElementById('directorateFilter').value;
    var rows = document.querySelectorAll('#servicesTableBody tr');
    
    rows.forEach(function(row) {
        if (filter === '' || row.getAttribute('data-directorate') === filter) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function filterPositions() {
    var filter = document.getElementById('serviceFilter').value;
    var rows = document.querySelectorAll('#positionsTableBody tr');
    
    rows.forEach(function(row) {
        if (filter === '' || row.getAttribute('data-service') === filter) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Edit and delete functions
function editDirectorate(id) { alert('تعديل المديرية رقم: ' + id); }
function deleteDirectorate(id) { if(confirm('هل أنت متأكد؟')) alert('حذف المديرية رقم: ' + id); }
function editService(id) { alert('تعديل المصلحة رقم: ' + id); }
function deleteService(id) { if(confirm('هل أنت متأكد؟')) alert('حذف المصلحة رقم: ' + id); }
function editPosition(id) { alert('تعديل الوظيفة رقم: ' + id); }
function deletePosition(id) { if(confirm('هل أنت متأكد؟')) alert('حذف الوظيفة رقم: ' + id); }

// Form submissions
$('#addDirectorateForm, #addServiceForm, #addPositionForm').on('submit', function(e) {
    e.preventDefault();
    alert('تم الحفظ بنجاح!');
    $(this).closest('.modal').modal('hide');
    location.reload();
});
</script>
{% endblock %}

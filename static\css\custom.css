/* تخصيص إضافي لبرنامج تسيير مستخدمي الجمارك الجزائرية */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

/* تحسين الشريط الجانبي */
.main-sidebar {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.nav-sidebar .nav-link {
    color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    margin: 2px 8px;
    transition: all 0.3s ease;
}

.nav-sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateX(-5px);
}

.nav-sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.brand-link {
    background-color: rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px;
}

.brand-text {
    color: white !important;
    font-weight: 600;
    font-size: 1.1rem;
}

/* تحسين البطاقات */
.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    padding: 20px;
    font-weight: 600;
}

.card-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.card-body {
    padding: 25px;
}

/* تحسين الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
}

/* تحسين الجداول */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 15px;
}

.table td {
    padding: 15px;
    border-color: #f1f3f4;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.02);
}

/* تحسين النماذج */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* تحسين الشارات */
.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
}

/* تحسين صور الموظفين */
.employee-photo {
    width: 150px;
    height: 200px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border: 3px solid white;
    transition: all 0.3s ease;
}

.employee-photo:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* تحسين صناديق المعلومات */
.info-box {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.info-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.info-box-icon {
    border-radius: 12px 0 0 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تحسين التنبيهات */
.alert {
    border: none;
    border-radius: 12px;
    padding: 15px 20px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* تحسين التصفح */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 2px solid #e9ecef;
    color: #667eea;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

/* تحسين الصناديق الصغيرة */
.small-box {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.small-box:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.small-box .inner {
    padding: 20px;
}

.small-box .inner h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
}

.small-box .inner p {
    font-size: 1rem;
    font-weight: 500;
    margin: 5px 0 0 0;
}

.small-box-footer {
    background-color: rgba(0,0,0,0.1);
    color: rgba(255,255,255,0.8);
    padding: 10px;
    text-decoration: none;
    display: block;
    transition: all 0.3s ease;
}

.small-box-footer:hover {
    background-color: rgba(0,0,0,0.2);
    color: white;
}

/* تحسين رفع الملفات */
.photo-upload-area {
    border: 3px dashed #667eea;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    cursor: pointer;
    transition: all 0.3s ease;
}

.photo-upload-area:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    border-color: #5a6fd8;
    transform: translateY(-2px);
}

.photo-upload-area.dragover {
    background: linear-gradient(135deg, #cce7ff 0%, #b3d9ff 100%);
    border-color: #0056b3;
    transform: scale(1.02);
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
    border-radius: 12px;
    border: none;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    padding: 10px 0;
}

.dropdown-item {
    padding: 10px 20px;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 2px 8px;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* تحسين الشريط العلوي */
.main-header {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-bottom: none;
}

.navbar-light .navbar-nav .nav-link {
    color: #495057;
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-light .navbar-nav .nav-link:hover {
    color: #667eea;
}

/* تحسين التذييل */
.main-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
    padding: 20px;
    color: #6c757d;
}

/* تحسين النوافذ المنبثقة */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    border-radius: 12px 12px 0 0;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 20px 25px;
}

/* تحسين الرسوم المتحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card, .small-box, .info-box {
    animation: fadeInUp 0.6s ease-out;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .card-body {
        padding: 15px;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
    
    .table td, .table th {
        padding: 10px;
        font-size: 0.9rem;
    }
    
    .small-box .inner h3 {
        font-size: 2rem;
    }
    
    .employee-photo {
        width: 100px;
        height: 130px;
    }
}

/* تحسين طباعة الصفحات */
@media print {
    .main-sidebar,
    .main-header,
    .main-footer,
    .btn,
    .card-tools {
        display: none !important;
    }
    
    .content-wrapper {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

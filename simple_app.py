#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق مبسط لاختبار برنامج تسيير مستخدمي الجمارك الجزائرية
"""

from flask import Flask, render_template, request, redirect, url_for, flash
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-secret-key'

# إنشاء مجلد uploads إذا لم يكن موجوداً
upload_folder = os.path.join('static', 'uploads')
os.makedirs(upload_folder, exist_ok=True)

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html', 
                         total_employees=150,
                         active_employees=140)

@app.route('/employees')
def employees_list():
    """قائمة الموظفين"""
    # بيانات وهمية للاختبار
    employees = []
    search = request.args.get('search', '')
    
    # محاكاة بيانات الموظفين
    class MockEmployee:
        def __init__(self, id, reg_num, first_name, last_name, gender, birth_date, rank, position, status):
            self.id = id
            self.registration_number = reg_num
            self.first_name = first_name
            self.last_name = last_name
            self.gender = gender
            self.birth_date = birth_date
            self.current_rank = MockRank(rank)
            self.current_position = MockPosition(position)
            self.status = status
            self.photo = None
            self.nom_fr = None
            self.prenom_fr = None
            
        @property
        def full_name(self):
            return f"{self.first_name} {self.last_name}"
        
        @property
        def age(self):
            from datetime import date
            today = date.today()
            return today.year - self.birth_date.year
    
    class MockRank:
        def __init__(self, name):
            self.name = name
    
    class MockPosition:
        def __init__(self, name):
            self.name = name
    
    class MockPagination:
        def __init__(self, items):
            self.items = items
            self.pages = 1
            self.page = 1
            self.has_prev = False
            self.has_next = False
            self.total = len(items)
            self.per_page = 20
    
    # إضافة بيانات وهمية
    from datetime import date
    mock_employees = [
        MockEmployee(1, '123456', 'أحمد', 'بن علي', 'ذكر', date(1985, 1, 15), 'مفتش للجمارك', 'مكلف بالموارد البشرية', 'نشط'),
        MockEmployee(2, '234567', 'فاطمة', 'بن محمد', 'أنثى', date(1990, 3, 20), 'عون جمارك', 'كاتبة', 'نشط'),
        MockEmployee(3, '345678', 'محمد', 'العربي', 'ذكر', date(1982, 7, 10), 'مفتش رئيسي للجمارك', 'رئيس مكتب', 'نشط'),
    ]
    
    employees = MockPagination(mock_employees)
    
    return render_template('employees/list.html', employees=employees, search=search)

@app.route('/employee/add')
def add_employee():
    """صفحة إضافة موظف"""
    return render_template('employees/add.html', form=None)

@app.route('/employee/<int:id>')
def employee_detail(id):
    """تفاصيل الموظف"""
    # بيانات وهمية
    class MockEmployee:
        def __init__(self):
            self.id = id
            self.registration_number = '123456'
            self.first_name = 'أحمد'
            self.last_name = 'بن علي'
            self.full_name = 'أحمد بن علي'
            self.gender = 'ذكر'
            self.birth_date = date(1985, 1, 15)
            self.age = 39
            self.status = 'نشط'
            self.photo = None
            self.phone1 = '0555123456'
            self.email = '<EMAIL>'
            self.primary_address = 'شارع الاستقلال، الجزائر'
            self.marital_status = 'متزوج'
            self.children_count = 2
            self.dependents_count = 0
            self.blood_type = 'A+'
            self.social_security_number = '185011601234567'
            self.national_id_number = '185011601234567890'
            self.hiring_date = date(2010, 9, 1)
            
            # علاقات وهمية
            self.birth_wilaya = MockWilaya('الجزائر')
            self.birth_commune = MockCommune('الجزائر الوسطى')
            self.corps = MockCorps('أسلاك خاصة')
            self.current_rank = MockRank('مفتش للجمارك')
            self.current_position = MockPosition('مكلف بالموارد البشرية')
            self.directorate = MockDirectorate('المديرية الجهوية للجمارك')
            self.service = MockService('مكتب تسيير المستخدمين')
            
            # قوائم فارغة للعلاقات
            self.spouses = []
            self.children = []
    
    class MockWilaya:
        def __init__(self, name):
            self.name_ar = name
    
    class MockCommune:
        def __init__(self, name):
            self.name_ar = name
    
    class MockCorps:
        def __init__(self, name):
            self.name = name
    
    class MockRank:
        def __init__(self, name):
            self.name = name
    
    class MockPosition:
        def __init__(self, name):
            self.name = name
    
    class MockDirectorate:
        def __init__(self, name):
            self.name = name
    
    class MockService:
        def __init__(self, name):
            self.name = name
    
    from datetime import date
    employee = MockEmployee()
    
    return render_template('employees/detail.html', employee=employee)

@app.route('/settings')
def settings():
    """صفحة الإعدادات"""
    return render_template('settings/index.html')

@app.route('/settings/administrative_divisions')
def administrative_divisions():
    """إدارة التقسيم الإداري"""
    # بيانات وهمية للولايات
    class MockWilaya:
        def __init__(self, id, code, name_ar, name_fr):
            self.id = id
            self.code = code
            self.name_ar = name_ar
            self.name_fr = name_fr
            self.communes = []
    
    wilayas = [
        MockWilaya(1, '16', 'الجزائر', 'Alger'),
        MockWilaya(2, '03', 'الأغواط', 'Laghouat'),
        MockWilaya(3, '31', 'وهران', 'Oran'),
    ]
    
    return render_template('settings/administrative_divisions.html', wilayas=wilayas)

@app.route('/settings/ranks')
def ranks_settings():
    """إدارة الرتب"""
    return render_template('settings/ranks.html')

@app.route('/settings/positions')
def positions_settings():
    """إدارة الوظائف"""
    return render_template('settings/positions.html')

@app.errorhandler(404)
def not_found(error):
    """صفحة الخطأ 404"""
    return render_template('base.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """صفحة الخطأ 500"""
    return render_template('base.html'), 500

if __name__ == '__main__':
    print("=" * 60)
    print("برنامج تسيير مستخدمي الجمارك الجزائرية - النسخة المبسطة")
    print("=" * 60)
    print("🚀 بدء تشغيل الخادم...")
    print("📍 العنوان: http://localhost:5000")
    print("للإيقاف اضغط Ctrl+C")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5000)

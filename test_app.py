#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات برنامج تسيير مستخدمي الجمارك الجزائرية
"""

import unittest
import tempfile
import os
from datetime import date, datetime
from app import app, db
from config import TestingConfig

class CustomsTestCase(unittest.TestCase):
    """فئة اختبارات التطبيق"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        app.config.from_object(TestingConfig)
        self.app = app.test_client()
        self.app_context = app.app_context()
        self.app_context.push()
        
        # إنشاء قاعدة البيانات
        db.create_all()
        
        # تهيئة البيانات الأساسية
        self.init_test_data()
    
    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def init_test_data(self):
        """تهيئة بيانات الاختبار"""
        from models import Wilaya, Commune, Corps, Rank, Directorate, Service, Position
        
        # إضافة ولاية للاختبار
        wilaya = Wilaya(code='16', name_ar='الجزائر', name_fr='Alger')
        db.session.add(wilaya)
        db.session.commit()
        
        # إضافة بلدية للاختبار
        commune = Commune(code='160101', name_ar='الجزائر الوسطى', name_fr='Alger Centre', wilaya_id=wilaya.id)
        db.session.add(commune)
        
        # إضافة سلك للاختبار
        corps = Corps(name='أسلاك خاصة بإدارة الجمارك', type='خاص')
        db.session.add(corps)
        db.session.commit()
        
        # إضافة رتبة للاختبار
        rank = Rank(name='مفتش للجمارك', corps_id=corps.id, level=11)
        db.session.add(rank)
        
        # إضافة مديرية للاختبار
        directorate = Directorate(name='المديرية الجهوية للجمارك', type='مديرية جهوية')
        db.session.add(directorate)
        db.session.commit()
        
        # إضافة مصلحة للاختبار
        service = Service(name='مكتب تسيير المستخدمين', directorate_id=directorate.id)
        db.session.add(service)
        db.session.commit()
        
        # إضافة وظيفة للاختبار
        position = Position(name='مكلف بالموارد البشرية', service_id=service.id)
        db.session.add(position)
        
        db.session.commit()
    
    def test_index_page(self):
        """اختبار الصفحة الرئيسية"""
        response = self.app.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn('لوحة التحكم', response.get_data(as_text=True))
    
    def test_employees_list_page(self):
        """اختبار صفحة قائمة الموظفين"""
        response = self.app.get('/employees')
        self.assertEqual(response.status_code, 200)
        self.assertIn('قائمة الموظفين', response.get_data(as_text=True))
    
    def test_add_employee_page(self):
        """اختبار صفحة إضافة موظف"""
        response = self.app.get('/employee/add')
        self.assertEqual(response.status_code, 200)
        self.assertIn('إضافة موظف جديد', response.get_data(as_text=True))
    
    def test_settings_page(self):
        """اختبار صفحة الإعدادات"""
        response = self.app.get('/settings')
        self.assertEqual(response.status_code, 200)
        self.assertIn('الإعدادات', response.get_data(as_text=True))
    
    def test_administrative_divisions_page(self):
        """اختبار صفحة التقسيم الإداري"""
        response = self.app.get('/settings/administrative_divisions')
        self.assertEqual(response.status_code, 200)
        self.assertIn('التقسيم الإداري', response.get_data(as_text=True))
    
    def test_create_employee(self):
        """اختبار إنشاء موظف جديد"""
        from models import Wilaya, Commune, Corps, Rank, Directorate, Service, Position
        
        # الحصول على البيانات المطلوبة
        wilaya = Wilaya.query.first()
        commune = Commune.query.first()
        corps = Corps.query.first()
        rank = Rank.query.first()
        directorate = Directorate.query.first()
        service = Service.query.first()
        position = Position.query.first()
        
        # بيانات الموظف للاختبار
        employee_data = {
            'registration_number': '123456',
            'last_name': 'بن علي',
            'first_name': 'أحمد',
            'nom_fr': 'Ben Ali',
            'prenom_fr': 'Ahmed',
            'gender': 'ذكر',
            'birth_date': '1985-01-15',
            'birth_wilaya_id': wilaya.id,
            'birth_commune_id': commune.id,
            'marital_status': 'متزوج',
            'children_count': 2,
            'dependents_count': 0,
            'blood_type': 'A+',
            'phone1': '0555123456',
            'email': '<EMAIL>',
            'primary_address': 'شارع الاستقلال، الجزائر',
            'status': 'نشط',
            'corps_id': corps.id,
            'current_rank_id': rank.id,
            'current_position_id': position.id,
            'directorate_id': directorate.id,
            'service_id': service.id,
            'hiring_date': '2010-09-01',
            'hiring_rank_id': rank.id,
            'social_security_number': '185011601234567',
            'national_id_number': '18501160**********',
            'csrf_token': 'test_token'
        }
        
        # محاولة إنشاء الموظف (سيفشل بسبب CSRF ولكن سنتحقق من الاستجابة)
        response = self.app.post('/employee/add', data=employee_data)
        # في بيئة الاختبار، CSRF معطل، لذا يجب أن ينجح
        self.assertIn(response.status_code, [200, 302])  # 200 للنموذج مع أخطاء، 302 للإعادة التوجيه
    
    def test_social_security_validation(self):
        """اختبار التحقق من رقم الضمان الاجتماعي"""
        from app import validate_social_security_number
        
        # رقم صحيح
        valid_ssn = '185011601234567'
        self.assertTrue(validate_social_security_number(valid_ssn, 1985, 'ذكر'))
        
        # رقم خاطئ (طول غير صحيح)
        invalid_ssn = '12345'
        self.assertFalse(validate_social_security_number(invalid_ssn, 1985, 'ذكر'))
        
        # رقم خاطئ (جنس غير مطابق)
        invalid_gender_ssn = '***************'  # 2 للأنثى
        self.assertFalse(validate_social_security_number(invalid_gender_ssn, 1985, 'ذكر'))
    
    def test_postal_account_validation(self):
        """اختبار التحقق من رقم الحساب الجاري البريدي"""
        from app import validate_postal_account
        
        # رقم صحيح (مثال)
        valid_account = '**********'
        # ملاحظة: هذا مثال، الخوارزمية الفعلية قد تختلف
        result = validate_postal_account(valid_account)
        self.assertIsInstance(result, bool)
        
        # رقم خاطئ (طول غير صحيح)
        invalid_account = '12345'
        self.assertFalse(validate_postal_account(invalid_account))
    
    def test_database_models(self):
        """اختبار نماذج قاعدة البيانات"""
        from models import Employee, Wilaya, Commune, Corps, Rank
        
        # التحقق من وجود البيانات الأساسية
        self.assertGreater(Wilaya.query.count(), 0)
        self.assertGreater(Commune.query.count(), 0)
        self.assertGreater(Corps.query.count(), 0)
        self.assertGreater(Rank.query.count(), 0)
        
        # اختبار العلاقات
        wilaya = Wilaya.query.first()
        self.assertIsNotNone(wilaya.communes)
        
        corps = Corps.query.first()
        self.assertIsNotNone(corps.ranks)
    
    def test_employee_age_calculation(self):
        """اختبار حساب عمر الموظف"""
        from models import Employee
        
        # إنشاء موظف للاختبار
        employee = Employee()
        employee.birth_date = date(1985, 1, 15)
        
        # حساب العمر
        age = employee.age
        current_year = date.today().year
        expected_age = current_year - 1985
        
        # التحقق من صحة الحساب (مع مراعاة الشهر واليوم)
        self.assertIn(age, [expected_age - 1, expected_age])
    
    def test_file_upload_validation(self):
        """اختبار التحقق من رفع الملفات"""
        # اختبار امتدادات الملفات المسموحة
        allowed_extensions = app.config.get('ALLOWED_EXTENSIONS', {'png', 'jpg', 'jpeg', 'gif'})
        
        self.assertIn('jpg', allowed_extensions)
        self.assertIn('png', allowed_extensions)
        self.assertNotIn('exe', allowed_extensions)
    
    def test_config_values(self):
        """اختبار قيم التكوين"""
        from config import CUSTOMS_CONFIG
        
        # التحقق من وجود الإعدادات المطلوبة
        self.assertIn('MIN_AGE', CUSTOMS_CONFIG)
        self.assertIn('MAX_AGE', CUSTOMS_CONFIG)
        self.assertIn('REGISTRATION_NUMBER_LENGTH', CUSTOMS_CONFIG)
        
        # التحقق من القيم المنطقية
        self.assertEqual(CUSTOMS_CONFIG['MIN_AGE'], 19)
        self.assertEqual(CUSTOMS_CONFIG['MAX_AGE'], 65)
        self.assertEqual(CUSTOMS_CONFIG['REGISTRATION_NUMBER_LENGTH'], 6)

class UtilityFunctionsTest(unittest.TestCase):
    """اختبارات الدوال المساعدة"""
    
    def test_secure_filename(self):
        """اختبار تأمين أسماء الملفات"""
        from werkzeug.utils import secure_filename
        
        # اسم ملف عادي
        safe_name = secure_filename('photo.jpg')
        self.assertEqual(safe_name, 'photo.jpg')
        
        # اسم ملف مع مسافات
        safe_name = secure_filename('my photo.jpg')
        self.assertEqual(safe_name, 'my_photo.jpg')
        
        # اسم ملف مع أحرف خاصة
        unsafe_name = '../../../etc/passwd'
        safe_name = secure_filename(unsafe_name)
        self.assertNotIn('..', safe_name)
        self.assertNotIn('/', safe_name)

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("=" * 60)
    print("تشغيل اختبارات برنامج تسيير مستخدمي الجمارك الجزائرية")
    print("=" * 60)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات التطبيق
    test_suite.addTest(unittest.makeSuite(CustomsTestCase))
    test_suite.addTest(unittest.makeSuite(UtilityFunctionsTest))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # طباعة النتائج
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ جميع الاختبارات نجحت!")
    else:
        print("❌ بعض الاختبارات فشلت:")
        print(f"   - الفشل: {len(result.failures)}")
        print(f"   - الأخطاء: {len(result.errors)}")
    print("=" * 60)
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    exit(0 if success else 1)

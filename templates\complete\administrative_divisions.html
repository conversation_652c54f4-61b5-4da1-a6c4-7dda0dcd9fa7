{% extends "professional_base.html" %}

{% block title %}التقسيم الإداري - نظام إدارة الجمارك الجزائرية{% endblock %}

{% block page_title %}التقسيم الإداري{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item"><a href="{{ url_for('settings') }}">الإعدادات</a></li>
<li class="breadcrumb-item active">التقسيم الإداري</li>
{% endblock %}

{% block content %}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-map-marked-alt mr-2"></i>
            التقسيم الإداري للجزائر
        </h1>
        <p class="text-muted">إدارة الولايات والبلديات</p>
    </div>
    <div class="col-md-4 text-left">
        <button class="btn btn-primary" data-toggle="modal" data-target="#addWilayaModal">
            <i class="fas fa-plus mr-1"></i>
            إضافة ولاية
        </button>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="stats-card primary">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">إجمالي الولايات</div>
                    <div class="stats-number">{{ wilayas|length }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-map stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="stats-card success">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">إجمالي البلديات</div>
                    <div class="stats-number">1541</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-city stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Wilayas Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table mr-2"></i>
                    قائمة الولايات
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="wilayasTable">
                        <thead>
                            <tr>
                                <th>الرمز</th>
                                <th>الاسم بالعربية</th>
                                <th>الاسم بالفرنسية</th>
                                <th>عدد البلديات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for wilaya in wilayas %}
                            <tr>
                                <td>
                                    <span class="badge badge-primary">{{ wilaya.code }}</span>
                                </td>
                                <td>
                                    <strong>{{ wilaya.name_ar }}</strong>
                                </td>
                                <td>
                                    <em>{{ wilaya.name_fr }}</em>
                                </td>
                                <td>
                                    <span class="badge badge-info">{{ wilaya.communes|length }}</span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewCommunes({{ wilaya.id }}, '{{ wilaya.name_ar }}')" title="عرض البلديات">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="editWilaya({{ wilaya.id }})" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-success" onclick="addCommune({{ wilaya.id }}, '{{ wilaya.name_ar }}')" title="إضافة بلدية">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Wilaya Modal -->
<div class="modal fade" id="addWilayaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة ولاية جديدة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addWilayaForm">
                    <div class="form-group">
                        <label for="wilayaCode">رمز الولاية</label>
                        <input type="text" class="form-control" id="wilayaCode" maxlength="2" required>
                    </div>
                    <div class="form-group">
                        <label for="wilayaNameAr">الاسم بالعربية</label>
                        <input type="text" class="form-control" id="wilayaNameAr" required>
                    </div>
                    <div class="form-group">
                        <label for="wilayaNameFr">الاسم بالفرنسية</label>
                        <input type="text" class="form-control" id="wilayaNameFr" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveWilaya()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Communes Modal -->
<div class="modal fade" id="communesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="communesModalTitle">بلديات الولاية</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="communesContent">
                    <!-- سيتم تحميل البلديات هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تفعيل DataTable
    $('#wilayasTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "pageLength": 25,
        "order": [[ 0, "asc" ]]
    });
});

function viewCommunes(wilayaId, wilayaName) {
    $('#communesModalTitle').text('بلديات ولاية ' + wilayaName);
    $('#communesContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>');
    $('#communesModal').modal('show');
    
    // محاكاة تحميل البلديات
    setTimeout(function() {
        $('#communesContent').html(`
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>الرمز</th>
                            <th>الاسم بالعربية</th>
                            <th>الاسم بالفرنسية</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>001</td>
                            <td>بلدية مركزية</td>
                            <td>Commune Centrale</td>
                        </tr>
                        <tr>
                            <td>002</td>
                            <td>بلدية فرعية</td>
                            <td>Commune Secondaire</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="text-center mt-3">
                <button class="btn btn-primary btn-sm" onclick="addCommune(${wilayaId}, '${wilayaName}')">
                    <i class="fas fa-plus mr-1"></i>
                    إضافة بلدية جديدة
                </button>
            </div>
        `);
    }, 1000);
}

function editWilaya(wilayaId) {
    alert('تعديل الولاية رقم: ' + wilayaId);
}

function addCommune(wilayaId, wilayaName) {
    alert('إضافة بلدية جديدة لولاية: ' + wilayaName);
}

function saveWilaya() {
    const code = $('#wilayaCode').val();
    const nameAr = $('#wilayaNameAr').val();
    const nameFr = $('#wilayaNameFr').val();
    
    if (!code || !nameAr || !nameFr) {
        alert('يرجى ملء جميع الحقول');
        return;
    }
    
    // هنا يمكن إضافة كود الحفظ الفعلي
    alert('تم حفظ الولاية بنجاح');
    $('#addWilayaModal').modal('hide');
    location.reload();
}
</script>
{% endblock %}

{% extends "professional_base.html" %}

{% block title %}قائمة الموظفين المتكاملة - نظام إدارة الجمارك الجزائرية{% endblock %}

{% block page_title %}قائمة الموظفين{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item">إدارة الموظفين</li>
<li class="breadcrumb-item active">قائمة الموظفين</li>
{% endblock %}

{% block content %}

<!-- Page Header with Actions -->
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">
            <i class="fas fa-users mr-2"></i>
            قائمة الموظفين
        </h1>
        <p class="text-muted">إدارة شاملة لجميع الموظفين</p>
    </div>
    <div class="col-md-6 text-left">
        <div class="btn-group" role="group">
            <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
                <i class="fas fa-plus mr-1"></i>
                إضافة موظف جديد
            </a>
            <button type="button" class="btn btn-success" onclick="exportData()">
                <i class="fas fa-file-excel mr-1"></i>
                تصدير Excel
            </button>
            <button type="button" class="btn btn-info" onclick="printList()">
                <i class="fas fa-print mr-1"></i>
                طباعة
            </button>
        </div>
    </div>
</div>

<!-- Advanced Search and Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search mr-2"></i>
                    البحث والتصفية المتقدمة
                    <button class="btn btn-sm btn-outline-secondary float-left" type="button" data-toggle="collapse" data-target="#advancedSearch">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h5>
            </div>
            <div class="collapse show" id="advancedSearch">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('employees_list') }}" id="searchForm">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search">البحث العام</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           value="{{ search }}" placeholder="الاسم، رقم التسجيل، البريد الإلكتروني">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="status">الحالة</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="">جميع الحالات</option>
                                        <option value="نشط" {% if status_filter == 'نشط' %}selected{% endif %}>نشط</option>
                                        <option value="في إجازة" {% if status_filter == 'في إجازة' %}selected{% endif %}>في إجازة</option>
                                        <option value="معلق" {% if status_filter == 'معلق' %}selected{% endif %}>معلق</option>
                                        <option value="متقاعد" {% if status_filter == 'متقاعد' %}selected{% endif %}>متقاعد</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="gender">الجنس</label>
                                    <select class="form-control" id="gender" name="gender">
                                        <option value="">الكل</option>
                                        <option value="ذكر" {% if gender_filter == 'ذكر' %}selected{% endif %}>ذكر</option>
                                        <option value="أنثى" {% if gender_filter == 'أنثى' %}selected{% endif %}>أنثى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="directorate">المديرية</label>
                                    <select class="form-control" id="directorate" name="directorate">
                                        <option value="">جميع المديريات</option>
                                        {% for directorate in directorates %}
                                        <option value="{{ directorate.id }}" {% if directorate_filter == directorate.id %}selected{% endif %}>
                                            {{ directorate.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div class="d-flex">
                                        <button type="submit" class="btn btn-primary mr-2">
                                            <i class="fas fa-search mr-1"></i>
                                            بحث
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                            <i class="fas fa-times mr-1"></i>
                                            مسح
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card primary">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">إجمالي النتائج</div>
                    <div class="stats-number">{{ employees.total }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-users stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card success">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">النشطين</div>
                    <div class="stats-number">{{ employees.items | selectattr('status', 'equalto', 'نشط') | list | length }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-user-check stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card warning">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">في إجازة</div>
                    <div class="stats-number">{{ employees.items | selectattr('status', 'equalto', 'في إجازة') | list | length }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-calendar-times stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card danger">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">معلق/متقاعد</div>
                    <div class="stats-number">{{ employees.items | rejectattr('status', 'in', ['نشط', 'في إجازة']) | list | length }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-user-times stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Employees Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table mr-2"></i>
                    قائمة الموظفين
                    <span class="badge badge-primary mr-2">{{ employees.total }} موظف</span>
                </h5>
                <div class="card-tools">
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-tool" onclick="toggleView('table')" id="tableViewBtn">
                            <i class="fas fa-table"></i>
                        </button>
                        <button type="button" class="btn btn-tool" onclick="toggleView('cards')" id="cardsViewBtn">
                            <i class="fas fa-th-large"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                {% if employees.items %}
                
                <!-- Table View -->
                <div id="tableView" class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th width="60">الصورة</th>
                                <th>رقم التسجيل</th>
                                <th>الاسم الكامل</th>
                                <th>الجنس</th>
                                <th>العمر</th>
                                <th>الرتبة</th>
                                <th>الوظيفة</th>
                                <th>المديرية</th>
                                <th>الحالة</th>
                                <th width="120">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees.items %}
                            <tr>
                                <td>
                                    {% if employee.photo %}
                                        <img src="{{ url_for('static', filename='photos/' + employee.photo) }}" 
                                             class="img-circle" width="40" height="40" alt="صورة الموظف">
                                    {% else %}
                                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px; font-weight: bold; font-size: 14px;">
                                            {{ employee.first_name[0] }}{{ employee.last_name[0] }}
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong class="text-primary">{{ employee.registration_number }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ employee.full_name }}</strong>
                                        {% if employee.email %}
                                        <br><small class="text-muted">{{ employee.email }}</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <i class="fas fa-{{ 'mars' if employee.gender == 'ذكر' else 'venus' }} 
                                       text-{{ 'primary' if employee.gender == 'ذكر' else 'danger' }}"></i>
                                    {{ employee.gender }}
                                </td>
                                <td>{{ employee.age }} سنة</td>
                                <td>{{ employee.current_rank.name }}</td>
                                <td>{{ employee.current_position.name }}</td>
                                <td>{{ employee.directorate.name }}</td>
                                <td>
                                    {% if employee.status == 'نشط' %}
                                        <span class="badge badge-success">{{ employee.status }}</span>
                                    {% elif employee.status == 'في إجازة' %}
                                        <span class="badge badge-warning">{{ employee.status }}</span>
                                    {% elif employee.status == 'معلق' %}
                                        <span class="badge badge-danger">{{ employee.status }}</span>
                                    {% else %}
                                        <span class="badge badge-secondary">{{ employee.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('employee_detail', id=employee.id) }}" 
                                           class="btn btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('edit_employee', id=employee.id) }}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" title="حذف"
                                                onclick="confirmDelete({{ employee.id }}, '{{ employee.full_name }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Cards View (Hidden by default) -->
                <div id="cardsView" class="row p-3" style="display: none;">
                    {% for employee in employees.items %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                {% if employee.photo %}
                                    <img src="{{ url_for('static', filename='photos/' + employee.photo) }}" 
                                         class="rounded-circle mb-3" width="80" height="80" alt="صورة الموظف">
                                {% else %}
                                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                                         style="width: 80px; height: 80px; font-weight: bold; font-size: 24px;">
                                        {{ employee.first_name[0] }}{{ employee.last_name[0] }}
                                    </div>
                                {% endif %}
                                <h6 class="card-title">{{ employee.full_name }}</h6>
                                <p class="card-text">
                                    <small class="text-muted">{{ employee.registration_number }}</small><br>
                                    <small>{{ employee.current_position.name }}</small><br>
                                    <small>{{ employee.directorate.name }}</small>
                                </p>
                                <div class="mb-2">
                                    {% if employee.status == 'نشط' %}
                                        <span class="badge badge-success">{{ employee.status }}</span>
                                    {% elif employee.status == 'في إجازة' %}
                                        <span class="badge badge-warning">{{ employee.status }}</span>
                                    {% else %}
                                        <span class="badge badge-secondary">{{ employee.status }}</span>
                                    {% endif %}
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('employee_detail', id=employee.id) }}" class="btn btn-primary">عرض</a>
                                    <a href="{{ url_for('edit_employee', id=employee.id) }}" class="btn btn-warning">تعديل</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد موظفين</h5>
                    <p class="text-muted">لم يتم العثور على أي موظفين بالمعايير المحددة</p>
                    <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
                        <i class="fas fa-plus mr-1"></i>
                        إضافة موظف جديد
                    </a>
                </div>
                {% endif %}
            </div>
            
            <!-- Pagination -->
            {% if employees.pages > 1 %}
            <div class="card-footer">
                <nav aria-label="تنقل الصفحات">
                    <ul class="pagination justify-content-center mb-0">
                        {% if employees.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees_list', page=employees.prev_num, search=search, status=status_filter, directorate=directorate_filter, gender=gender_filter) }}">
                                    <i class="fas fa-chevron-right"></i> السابق
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in employees.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != employees.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('employees_list', page=page_num, search=search, status=status_filter, directorate=directorate_filter, gender=gender_filter) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if employees.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees_list', page=employees.next_num, search=search, status=status_filter, directorate=directorate_filter, gender=gender_filter) }}">
                                    التالي <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                <div class="text-center mt-2">
                    <small class="text-muted">
                        عرض {{ employees.per_page * (employees.page - 1) + 1 }} إلى 
                        {{ employees.per_page * employees.page if employees.page < employees.pages else employees.total }} 
                        من أصل {{ employees.total }} موظف
                    </small>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// تبديل العرض بين الجدول والبطاقات
function toggleView(viewType) {
    if (viewType === 'table') {
        $('#tableView').show();
        $('#cardsView').hide();
        $('#tableViewBtn').addClass('active');
        $('#cardsViewBtn').removeClass('active');
    } else {
        $('#tableView').hide();
        $('#cardsView').show();
        $('#tableViewBtn').removeClass('active');
        $('#cardsViewBtn').addClass('active');
    }
}

// مسح الفلاتر
function clearFilters() {
    $('#search').val('');
    $('#status').val('');
    $('#gender').val('');
    $('#directorate').val('');
    $('#searchForm').submit();
}

// تأكيد الحذف
function confirmDelete(employeeId, employeeName) {
    if (confirm('هل أنت متأكد من حذف الموظف: ' + employeeName + '؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        // هنا يمكن إضافة كود الحذف الفعلي
        alert('سيتم تنفيذ عملية الحذف...');
    }
}

// تصدير البيانات
function exportData() {
    alert('سيتم تصدير البيانات إلى ملف Excel...');
}

// طباعة القائمة
function printList() {
    window.print();
}

$(document).ready(function() {
    // تطبيق الفلاتر تلقائياً عند التغيير
    $('#status, #gender, #directorate').change(function() {
        $('#searchForm').submit();
    });
    
    // البحث السريع
    let searchTimeout;
    $('#search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            $('#searchForm').submit();
        }, 1000);
    });
    
    // تفعيل tooltips
    $('[title]').tooltip();
});
</script>
{% endblock %}

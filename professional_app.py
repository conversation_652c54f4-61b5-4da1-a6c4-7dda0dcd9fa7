#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الجمارك الجزائرية - التصميم الاحترافي
Professional Algerian Customs Management System
"""

from flask import Flask, render_template, request, redirect, url_for, flash
from datetime import date, datetime
import os

print("=" * 70)
print("🇩🇿 نظام إدارة الجمارك الجزائرية - النسخة الاحترافية")
print("   Professional Algerian Customs Management System")
print("=" * 70)

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'professional-customs-management-2024'

# إنشاء مجلد uploads
upload_folder = os.path.join('static', 'uploads')
os.makedirs(upload_folder, exist_ok=True)

# بيانات وهمية للاختبار
class MockEmployee:
    def __init__(self, id, reg_num, first_name, last_name, gender, birth_date, rank, position, status, department=None):
        self.id = id
        self.registration_number = reg_num
        self.first_name = first_name
        self.last_name = last_name
        self.gender = gender
        self.birth_date = birth_date
        self.current_rank = MockRank(rank)
        self.current_position = MockPosition(position)
        self.status = status
        self.department = department or "الإدارة العامة"
        self.photo = None
        self.phone1 = f"055512345{id}"
        self.email = f"{first_name.lower()}@douane.gov.dz"
        
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def age(self):
        today = date.today()
        return today.year - self.birth_date.year

class MockRank:
    def __init__(self, name):
        self.name = name

class MockPosition:
    def __init__(self, name):
        self.name = name

class MockPagination:
    def __init__(self, items):
        self.items = items
        self.pages = 1
        self.page = 1
        self.has_prev = False
        self.has_next = False
        self.total = len(items)
        self.per_page = 20

# بيانات الموظفين الوهمية
MOCK_EMPLOYEES = [
    MockEmployee(1, '001234', 'أحمد', 'بن علي', 'ذكر', date(1985, 1, 15), 'مفتش للجمارك', 'مكلف بالموارد البشرية', 'نشط', 'الموارد البشرية'),
    MockEmployee(2, '002345', 'فاطمة', 'بن محمد', 'أنثى', date(1990, 3, 20), 'عون جمارك', 'كاتبة', 'نشط', 'الإدارة'),
    MockEmployee(3, '003456', 'محمد', 'العربي', 'ذكر', date(1982, 7, 10), 'مفتش رئيسي للجمارك', 'رئيس مكتب', 'نشط', 'التفتيش'),
    MockEmployee(4, '004567', 'زينب', 'بن يوسف', 'أنثى', date(1988, 11, 5), 'مفتش للجمارك', 'مكلفة بالتكوين', 'نشط', 'التكوين'),
    MockEmployee(5, '005678', 'عبد الرحمن', 'بن سالم', 'ذكر', date(1983, 6, 18), 'مفتش رئيسي للجمارك', 'رئيس مصلحة', 'نشط', 'المالية'),
    MockEmployee(6, '006789', 'سارة', 'أحمد', 'أنثى', date(1992, 9, 12), 'عون جمارك', 'محاسبة', 'نشط', 'المالية'),
    MockEmployee(7, '007890', 'يوسف', 'محمد', 'ذكر', date(1987, 4, 25), 'مفتش للجمارك', 'مفتش ميداني', 'نشط', 'التفتيش'),
    MockEmployee(8, '008901', 'نادية', 'علي', 'أنثى', date(1989, 12, 8), 'مفتش للجمارك', 'مكلفة بالإحصائيات', 'في إجازة', 'الإحصائيات'),
]

@app.route('/')
def index():
    """الصفحة الرئيسية - لوحة التحكم"""
    total_employees = len(MOCK_EMPLOYEES)
    active_employees = len([emp for emp in MOCK_EMPLOYEES if emp.status == 'نشط'])
    
    return render_template('professional_base.html', 
                         total_employees=total_employees,
                         active_employees=active_employees)

@app.route('/dashboard')
def dashboard():
    """لوحة التحكم المفصلة"""
    total_employees = len(MOCK_EMPLOYEES)
    active_employees = len([emp for emp in MOCK_EMPLOYEES if emp.status == 'نشط'])
    
    return render_template('dashboard.html', 
                         total_employees=total_employees,
                         active_employees=active_employees)

@app.route('/employees')
def employees_list():
    """قائمة الموظفين"""
    search = request.args.get('search', '')
    status_filter = request.args.get('status', '')
    
    employees = MOCK_EMPLOYEES.copy()
    
    # تطبيق البحث
    if search:
        employees = [emp for emp in employees if 
                    search.lower() in emp.full_name.lower() or 
                    search in emp.registration_number]
    
    # تطبيق فلتر الحالة
    if status_filter:
        employees = [emp for emp in employees if emp.status == status_filter]
    
    employees_paginated = MockPagination(employees)
    
    return render_template('employees/professional_list.html', 
                         employees=employees_paginated, 
                         search=search,
                         status_filter=status_filter)

@app.route('/employee/add')
def add_employee():
    """صفحة إضافة موظف"""
    return render_template('employees/professional_add.html')

@app.route('/employee/<int:id>')
def employee_detail(id):
    """تفاصيل الموظف"""
    employee = next((emp for emp in MOCK_EMPLOYEES if emp.id == id), None)
    
    if not employee:
        flash('الموظف غير موجود', 'error')
        return redirect(url_for('employees_list'))
    
    return render_template('employees/professional_detail.html', employee=employee)

@app.route('/settings')
def settings():
    """صفحة الإعدادات"""
    return render_template('settings/professional_index.html')

@app.route('/settings/administrative_divisions')
def administrative_divisions():
    """إدارة التقسيم الإداري"""
    # بيانات وهمية للولايات
    class MockWilaya:
        def __init__(self, id, code, name_ar, name_fr):
            self.id = id
            self.code = code
            self.name_ar = name_ar
            self.name_fr = name_fr
            self.communes = []
    
    wilayas = [
        MockWilaya(1, '16', 'الجزائر', 'Alger'),
        MockWilaya(2, '03', 'الأغواط', 'Laghouat'),
        MockWilaya(3, '31', 'وهران', 'Oran'),
        MockWilaya(4, '25', 'قسنطينة', 'Constantine'),
        MockWilaya(5, '30', 'ورقلة', 'Ouargla'),
    ]
    
    return render_template('settings/professional_divisions.html', wilayas=wilayas)

@app.route('/settings/ranks')
def ranks_settings():
    """إدارة الرتب"""
    return render_template('settings/professional_ranks.html')

@app.route('/settings/positions')
def positions_settings():
    """إدارة الوظائف"""
    return render_template('settings/professional_positions.html')

@app.route('/reports')
def reports():
    """صفحة التقارير"""
    return render_template('reports/professional_index.html')

@app.errorhandler(404)
def not_found(error):
    """صفحة الخطأ 404"""
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """صفحة الخطأ 500"""
    return render_template('errors/500.html'), 500

# Context processors
@app.context_processor
def utility_processor():
    """إضافة دوال مساعدة للقوالب"""
    def format_date(date_obj):
        if date_obj:
            return date_obj.strftime('%d/%m/%Y')
        return ''
    
    def format_datetime(datetime_obj):
        if datetime_obj:
            return datetime_obj.strftime('%d/%m/%Y %H:%M')
        return ''
    
    def current_year():
        return datetime.now().year
    
    return dict(
        format_date=format_date,
        format_datetime=format_datetime,
        current_year=current_year,
        moment=datetime
    )

if __name__ == '__main__':
    print("\n" + "=" * 70)
    print("🎯 بدء تشغيل النظام الاحترافي...")
    print("📍 العنوان: http://localhost:5000")
    print("📍 لوحة التحكم: http://localhost:5000/dashboard")
    print("🔧 للإيقاف: اضغط Ctrl+C")
    print("=" * 70)
    print("✅ النظام الاحترافي جاهز!")
    print()
    
    try:
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
    finally:
        print("👋 شكراً لاستخدام نظام إدارة الجمارك الجزائرية")

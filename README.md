# برنامج تسيير مستخدمي الجمارك الجزائرية
## Algerian Customs Employees Management System

## نظرة عامة

برنامج شامل لإدارة وتسيير بيانات موظفي الجمارك الجزائرية، مطور باستخدام Python Flask مع قاعدة بيانات SQLite وواجهة AdminLTE 4 الاحترافية.

## حالة المشروع

✅ **المرحلة الأولى مكتملة** - تم إنشاء الهيكل الأساسي والواجهة الرئيسية

### ما تم إنجازه:
- ✅ هيكل المشروع الأساسي مع Flask
- ✅ قاعدة البيانات SQLite مع جميع الجداول المطلوبة
- ✅ واجهة AdminLTE 4 باللغة العربية
- ✅ نماذج البيانات (Models) الكاملة
- ✅ نماذج الإدخال (Forms) مع التحقق من البيانات
- ✅ صفحات الإعدادات الأساسية
- ✅ نظام التشغيل والاختبار

### قيد التطوير:
- 🔄 صفحات إدارة الموظفين التفصيلية
- 🔄 الملفات الفرعية للموظفين
- 🔄 نظام رفع وإدارة الصور
- 🔄 دوال التحقق المتقدمة

## المميزات الرئيسية

### إدارة الموظفين
- **البيانات الأساسية**: رقم التسجيل، الاسم، اللقب، الجنس، تاريخ الميلاد، الحالة العائلية
- **بيانات الاتصال**: أرقام الهاتف، البريد الإلكتروني، العناوين
- **البيانات المهنية**: السلك، الرتبة، الوظيفة، المديرية، المصلحة
- **الوثائق**: رقم الضمان الاجتماعي، بطاقة التعريف، رخصة السياقة
- **إدارة الصور**: رفع وضغط صور الموظفين

### الملفات الفرعية
- **الشهادات**: نوع الشهادة، التخصص، سنة المنح، المؤسسة المانحة
- **التكوين**: موضوع التكوين، المدة، تواريخ البداية والنهاية
- **اللغات**: مستوى الكتابة والقراءة لكل لغة
- **التحويلات والتنقلات**: تاريخ التنصيب، المدة، رقم المقرر
- **العطل**: السنوية، المرضية، الاستثنائية
- **الأحداث الوظيفية**: الاستيداع، التوقيف، الوفاة، الانتداب، الاستقالة
- **الترقيات**: في الرتبة والدرجة
- **الأسرة**: بيانات الزوجة والأولاد والمتكفل بهم
- **العقوبات والمكافآت**

### الإعدادات
- **التقسيم الإداري**: الولايات والبلديات الجزائرية
- **الهيكل التنظيمي**: المديريات، المصالح، الوظائف
- **الرتب والأسلاك**: حسب نظام الجمارك الجزائرية
- **إعدادات النظام**: بيانات المؤسسة

### التحقق والحسابات
- **التحقق من رقم الضمان الاجتماعي الجزائري**
- **التحقق من رقم الحساب الجاري البريدي**
- **حساب العمر والمدد تلقائياً**
- **التحقق من صحة التواريخ**

## متطلبات النظام

- Python 3.8+
- Flask 2.3+
- SQLite 3
- متصفح ويب حديث

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd customs-employees-management
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv
source venv/bin/activate  # على Linux/Mac
# أو
venv\Scripts\activate  # على Windows
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. تهيئة قاعدة البيانات
```bash
python init_db.py
```

### 5. تشغيل التطبيق
```bash
python app.py
```

### 6. فتح التطبيق
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## هيكل المشروع

```
customs-employees-management/
├── app.py                 # التطبيق الرئيسي
├── models.py             # نماذج قاعدة البيانات
├── forms.py              # نماذج الإدخال
├── init_db.py            # تهيئة قاعدة البيانات
├── requirements.txt      # المتطلبات
├── templates/            # قوالب HTML
│   ├── base.html
│   ├── index.html
│   ├── employees/
│   └── settings/
├── static/               # الملفات الثابتة
│   ├── css/
│   ├── js/
│   ├── img/
│   └── uploads/          # صور الموظفين
└── README.md
```

## قاعدة البيانات

### الجداول الرئيسية
- `employees`: بيانات الموظفين الأساسية
- `wilayas`: الولايات الجزائرية
- `communes`: البلديات
- `corps`: الأسلاك
- `ranks`: الرتب
- `directorates`: المديريات
- `services`: المصالح
- `positions`: الوظائف

### الجداول الفرعية
- `certificates`: الشهادات
- `trainings`: التكوين
- `languages`: اللغات
- `transfers`: التحويلات
- `annual_leaves`: العطل السنوية
- `sick_leaves`: العطل المرضية
- `other_leaves`: العطل الأخرى
- `punishments`: العقوبات
- `rewards`: المكافآت
- `spouses`: الزوجات
- `children`: الأولاد
- `dependents`: المتكفل بهم

## الواجهة

### التصميم
- **AdminLTE 4**: واجهة إدارية احترافية
- **Bootstrap 5**: تصميم متجاوب
- **Font Awesome**: أيقونات حديثة
- **دعم اللغة العربية**: RTL وخطوط عربية

### الصفحات الرئيسية
- **لوحة التحكم**: إحصائيات ومعلومات سريعة
- **قائمة الموظفين**: عرض وبحث وتصفية
- **إضافة موظف**: نموذج شامل لإدخال البيانات
- **تفاصيل الموظف**: عرض كامل لجميع البيانات
- **الإعدادات**: إدارة البيانات المرجعية

## المميزات التقنية

### الأمان
- حماية من CSRF
- تشفير كلمات المرور
- تحقق من صحة البيانات
- تنظيف المدخلات

### الأداء
- ضغط الصور تلقائياً
- فهرسة قاعدة البيانات
- تحميل البيانات بالصفحات
- تحسين الاستعلامات

### سهولة الاستخدام
- واجهة باللغة العربية
- رسائل خطأ واضحة
- تحقق فوري من البيانات
- تصميم متجاوب

## التطوير المستقبلي

### المميزات المخططة
- [ ] تصدير التقارير (PDF, Excel)
- [ ] نظام الصلاحيات والمستخدمين
- [ ] إشعارات العطل والترقيات
- [ ] نسخ احتياطية تلقائية
- [ ] API للتكامل مع أنظمة أخرى
- [ ] تطبيق موبايل

### التحسينات
- [ ] تحسين الأداء
- [ ] إضافة المزيد من التقارير
- [ ] تحسين واجهة المستخدم
- [ ] دعم قواعد بيانات أخرى

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني

## شكر وتقدير

- فريق AdminLTE لواجهة الإدارة الرائعة
- مجتمع Flask للإطار المرن
- جميع المساهمين في المشروع

---

**ملاحظة**: هذا المشروع مطور خصيصاً لإدارة الجمارك الجزائرية ويتبع اللوائح والقوانين المحلية.

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام المتكامل لإدارة الجمارك الجزائرية
Complete Algerian Customs Management System Launcher
"""

import os
import sys
from complete_app import app, db, init_database

def main():
    print("=" * 80)
    print("🇩🇿 نظام إدارة الجمارك الجزائرية - النسخة المتكاملة")
    print("   Complete Algerian Customs Management System")
    print("=" * 80)
    
    print("\n🔍 فحص متطلبات النظام...")
    
    # التحقق من Python
    print(f"   ✓ Python {sys.version.split()[0]}")
    
    # التحقق من المكتبات
    try:
        import flask
        print(f"   ✓ Flask {flask.__version__}")
    except ImportError:
        print("   ❌ Flask غير مثبت")
        print("   💡 قم بتشغيل: pip install -r requirements_complete.txt")
        return False
    
    try:
        import flask_sqlalchemy
        print("   ✓ SQLAlchemy متوفر")
    except ImportError:
        print("   ❌ SQLAlchemy غير مثبت")
        return False
    
    try:
        import flask_wtf
        print("   ✓ WTForms متوفر")
    except ImportError:
        print("   ❌ WTForms غير مثبت")
        return False
    
    print("\n🗄️ تهيئة قاعدة البيانات...")
    try:
        init_database()
        print("   ✓ تم تهيئة قاعدة البيانات بنجاح")
    except Exception as e:
        print(f"   ❌ خطأ في تهيئة قاعدة البيانات: {e}")
        return False
    
    print("\n📁 فحص المجلدات...")
    folders = ['static/uploads', 'static/documents', 'static/photos']
    for folder in folders:
        if os.path.exists(folder):
            print(f"   ✓ {folder}")
        else:
            os.makedirs(folder, exist_ok=True)
            print(f"   ✓ تم إنشاء {folder}")
    
    print("\n" + "=" * 80)
    print("🎯 بدء تشغيل النظام المتكامل...")
    print("📍 العنوان الرئيسي: http://localhost:5000")
    print("📍 لوحة التحكم: http://localhost:5000")
    print("📍 قائمة الموظفين: http://localhost:5000/employees")
    print("📍 إضافة موظف: http://localhost:5000/employee/add")
    print("🔧 للإيقاف: اضغط Ctrl+C")
    print("=" * 80)
    print("✅ النظام المتكامل جاهز للاستخدام!")
    print()
    
    return True

if __name__ == '__main__':
    if main():
        try:
            app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
        except Exception as e:
            print(f"\n❌ خطأ في تشغيل النظام: {e}")
            print("💡 تأكد من أن المنفذ 5000 غير مستخدم")
        finally:
            print("👋 شكراً لاستخدام نظام إدارة الجمارك الجزائرية")
    else:
        print("\n❌ فشل في تهيئة النظام")
        print("💡 تأكد من تثبيت جميع المتطلبات: pip install -r requirements_complete.txt")

{% extends "professional_base.html" %}

{% block title %}{{ title }} - نظام إدارة الجمارك الجزائرية{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item">إدارة الموظفين</li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-user-{{ 'edit' if employee else 'plus' }} mr-2"></i>
            {{ title }}
        </h1>
        <p class="text-muted">{{ 'تعديل بيانات الموظف الحالي' if employee else 'إضافة موظف جديد إلى النظام' }}</p>
    </div>
    <div class="col-md-4 text-left">
        <a href="{{ url_for('employees_list') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right mr-1"></i>
            العودة للقائمة
        </a>
        {% if employee %}
        <a href="{{ url_for('employee_detail', id=employee.id) }}" class="btn btn-info">
            <i class="fas fa-eye mr-1"></i>
            عرض التفاصيل
        </a>
        {% endif %}
    </div>
</div>

<form method="POST" enctype="multipart/form-data" id="employeeForm">
    {{ form.hidden_tag() }}
    
    <!-- Personal Information -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user mr-2"></i>
                        المعلومات الشخصية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.registration_number.label(class="form-label required") }}
                                {{ form.registration_number(class="form-control" + (" is-invalid" if form.registration_number.errors else "")) }}
                                {% if form.registration_number.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.registration_number.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.first_name.label(class="form-label required") }}
                                {{ form.first_name(class="form-control" + (" is-invalid" if form.first_name.errors else "")) }}
                                {% if form.first_name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.first_name.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.last_name.label(class="form-label required") }}
                                {{ form.last_name(class="form-control" + (" is-invalid" if form.last_name.errors else "")) }}
                                {% if form.last_name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.last_name.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.gender.label(class="form-label required") }}
                                {{ form.gender(class="form-control" + (" is-invalid" if form.gender.errors else "")) }}
                                {% if form.gender.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.gender.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.first_name_fr.label(class="form-label") }}
                                {{ form.first_name_fr(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.last_name_fr.label(class="form-label") }}
                                {{ form.last_name_fr(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.birth_date.label(class="form-label required") }}
                                {{ form.birth_date(class="form-control" + (" is-invalid" if form.birth_date.errors else "")) }}
                                {% if form.birth_date.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.birth_date.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.birth_place.label(class="form-label") }}
                                {{ form.birth_place(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.birth_wilaya_id.label(class="form-label") }}
                                {{ form.birth_wilaya_id(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.birth_commune_id.label(class="form-label") }}
                                {{ form.birth_commune_id(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.blood_type.label(class="form-label") }}
                                {{ form.blood_type(class="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-address-book mr-2"></i>
                        معلومات الاتصال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.phone1.label(class="form-label") }}
                                {{ form.phone1(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.phone2.label(class="form-label") }}
                                {{ form.phone2(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.email.label(class="form-label") }}
                                {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                {% if form.email.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.email.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.primary_address.label(class="form-label") }}
                                {{ form.primary_address(class="form-control", rows="3") }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.secondary_address.label(class="form-label") }}
                                {{ form.secondary_address(class="form-control", rows="3") }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Family Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-home mr-2"></i>
                        المعلومات العائلية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.marital_status.label(class="form-label") }}
                                {{ form.marital_status(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.children_count.label(class="form-label") }}
                                {{ form.children_count(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.dependents_count.label(class="form-label") }}
                                {{ form.dependents_count(class="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Official Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-id-card mr-2"></i>
                        المعلومات الرسمية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.social_security_number.label(class="form-label") }}
                                {{ form.social_security_number(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.national_id_number.label(class="form-label") }}
                                {{ form.national_id_number(class="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employment Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-briefcase mr-2"></i>
                        معلومات التوظيف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.hiring_date.label(class="form-label required") }}
                                {{ form.hiring_date(class="form-control" + (" is-invalid" if form.hiring_date.errors else "")) }}
                                {% if form.hiring_date.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.hiring_date.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.corps_id.label(class="form-label required") }}
                                {{ form.corps_id(class="form-control" + (" is-invalid" if form.corps_id.errors else "")) }}
                                {% if form.corps_id.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.corps_id.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.rank_id.label(class="form-label required") }}
                                {{ form.rank_id(class="form-control" + (" is-invalid" if form.rank_id.errors else "")) }}
                                {% if form.rank_id.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.rank_id.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.position_id.label(class="form-label required") }}
                                {{ form.position_id(class="form-control" + (" is-invalid" if form.position_id.errors else "")) }}
                                {% if form.position_id.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.position_id.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.directorate_id.label(class="form-label required") }}
                                {{ form.directorate_id(class="form-control" + (" is-invalid" if form.directorate_id.errors else "")) }}
                                {% if form.directorate_id.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.directorate_id.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.service_id.label(class="form-label") }}
                                {{ form.service_id(class="form-control") }}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.status.label(class="form-label") }}
                                {{ form.status(class="form-control") }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save mr-2"></i>
                        {{ 'تحديث البيانات' if employee else 'حفظ الموظف' }}
                    </button>
                    <a href="{{ url_for('employees_list') }}" class="btn btn-secondary btn-lg mr-3">
                        <i class="fas fa-times mr-2"></i>
                        إلغاء
                    </a>
                    {% if employee %}
                    <a href="{{ url_for('employee_detail', id=employee.id) }}" class="btn btn-info btn-lg mr-3">
                        <i class="fas fa-eye mr-2"></i>
                        عرض التفاصيل
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</form>

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث البلديات عند تغيير الولاية
    $('#birth_wilaya_id').change(function() {
        var wilayaId = $(this).val();
        if (wilayaId) {
            // هنا يمكن إضافة AJAX لجلب البلديات
            // مؤقتاً سنتركها فارغة
            $('#birth_commune_id').html('<option value="">اختر البلدية</option>');
        } else {
            $('#birth_commune_id').html('<option value="">اختر البلدية</option>');
        }
    });
    
    // تحديث المصالح عند تغيير المديرية
    $('#directorate_id').change(function() {
        var directorateId = $(this).val();
        if (directorateId) {
            // هنا يمكن إضافة AJAX لجلب المصالح
            // مؤقتاً سنتركها فارغة
            $('#service_id').html('<option value="">اختر المصلحة</option>');
        } else {
            $('#service_id').html('<option value="">اختر المصلحة</option>');
        }
    });
    
    // تحديث الرتب عند تغيير السلك
    $('#corps_id').change(function() {
        var corpsId = $(this).val();
        if (corpsId) {
            // هنا يمكن إضافة AJAX لجلب الرتب حسب السلك
        }
    });
    
    // التحقق من صحة النموذج قبل الإرسال
    $('#employeeForm').submit(function(e) {
        var isValid = true;
        var errors = [];
        
        // التحقق من الحقول المطلوبة
        $('input[required], select[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('is-invalid');
                isValid = false;
                errors.push($(this).prev('label').text() + ' مطلوب');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        // التحقق من صحة البريد الإلكتروني
        var email = $('#email').val();
        if (email && !isValidEmail(email)) {
            $('#email').addClass('is-invalid');
            isValid = false;
            errors.push('البريد الإلكتروني غير صحيح');
        }
        
        // التحقق من تاريخ الميلاد
        var birthDate = new Date($('#birth_date').val());
        var today = new Date();
        var age = today.getFullYear() - birthDate.getFullYear();
        
        if (age < 18 || age > 65) {
            $('#birth_date').addClass('is-invalid');
            isValid = false;
            errors.push('العمر يجب أن يكون بين 18 و 65 سنة');
        }
        
        if (!isValid) {
            e.preventDefault();
            alert('يرجى تصحيح الأخطاء التالية:\n' + errors.join('\n'));
            $('html, body').animate({
                scrollTop: $('.is-invalid').first().offset().top - 100
            }, 500);
        }
    });
    
    // إضافة تأثيرات بصرية
    $('.form-control').focus(function() {
        $(this).parent().addClass('focused');
    }).blur(function() {
        $(this).parent().removeClass('focused');
    });
});

// التحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
</script>

<style>
.required::after {
    content: " *";
    color: red;
}

.focused {
    background-color: #f8f9fa;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
{% endblock %}

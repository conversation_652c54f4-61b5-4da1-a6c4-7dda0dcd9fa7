/* Professional Customs Management System CSS */

/* RTL Support */
html, body {
    direction: rtl !important;
    text-align: right !important;
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    background: #f8f9fa !important;
    font-size: 14px;
}

/* Layout */
.wrapper {
    direction: rtl !important;
}

/* Sidebar */
.main-sidebar {
    right: 0 !important;
    left: auto !important;
    background: #2c3e50 !important;
    width: 250px !important;
    position: fixed !important;
    top: 0 !important;
    bottom: 0 !important;
    z-index: 1000 !important;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1) !important;
}

/* Content */
.content-wrapper {
    margin-right: 250px !important;
    margin-left: 0 !important;
    background: #ffffff !important;
    min-height: 100vh !important;
}

/* Navbar */
.main-header.navbar {
    margin-right: 250px !important;
    margin-left: 0 !important;
    background: #ffffff !important;
    border-bottom: 1px solid #e3e6f0 !important;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

/* Brand */
.brand-link {
    background: #34495e !important;
    color: white !important;
    padding: 15px !important;
    border-bottom: 1px solid #2c3e50 !important;
}

.brand-text {
    color: white !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
}

/* Navigation */
.nav-sidebar .nav-link {
    color: rgba(255,255,255,0.8) !important;
    padding: 12px 15px !important;
    margin: 2px 8px !important;
    border-radius: 5px !important;
    transition: all 0.3s ease !important;
}

.nav-sidebar .nav-link:hover {
    background: rgba(255,255,255,0.1) !important;
    color: white !important;
}

.nav-sidebar .nav-link.active {
    background: #3498db !important;
    color: white !important;
}

.nav-sidebar .nav-icon {
    float: right !important;
    margin-left: 10px !important;
    margin-right: 0 !important;
    width: 20px !important;
    text-align: center !important;
}

/* Cards */
.card {
    border: none !important;
    border-radius: 8px !important;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    margin-bottom: 20px !important;
}

.card-header {
    background: #f8f9fc !important;
    border-bottom: 1px solid #e3e6f0 !important;
    padding: 15px 20px !important;
    font-weight: 600 !important;
    color: #5a5c69 !important;
}

.card-body {
    padding: 20px !important;
}

/* Statistics Cards */
.stats-card {
    border-left: 4px solid #3498db !important;
    background: white !important;
    padding: 20px !important;
    border-radius: 8px !important;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    transition: all 0.3s ease !important;
}

.stats-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
}

.stats-card.primary {
    border-left-color: #3498db !important;
}

.stats-card.success {
    border-left-color: #27ae60 !important;
}

.stats-card.warning {
    border-left-color: #f39c12 !important;
}

.stats-card.danger {
    border-left-color: #e74c3c !important;
}

.stats-number {
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: #5a5c69 !important;
    margin-bottom: 5px !important;
}

.stats-label {
    color: #858796 !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
}

.stats-icon {
    font-size: 2rem !important;
    color: #dddfeb !important;
    float: left !important;
}

/* Buttons */
.btn {
    border-radius: 5px !important;
    font-weight: 500 !important;
    padding: 8px 16px !important;
    transition: all 0.3s ease !important;
}

.btn-primary {
    background: #3498db !important;
    border-color: #3498db !important;
}

.btn-primary:hover {
    background: #2980b9 !important;
    border-color: #2980b9 !important;
}

.btn-success {
    background: #27ae60 !important;
    border-color: #27ae60 !important;
}

.btn-success:hover {
    background: #229954 !important;
    border-color: #229954 !important;
}

.btn-warning {
    background: #f39c12 !important;
    border-color: #f39c12 !important;
}

.btn-warning:hover {
    background: #e67e22 !important;
    border-color: #e67e22 !important;
}

.btn-danger {
    background: #e74c3c !important;
    border-color: #e74c3c !important;
}

.btn-danger:hover {
    background: #c0392b !important;
    border-color: #c0392b !important;
}

/* Tables */
.table {
    color: #5a5c69 !important;
}

.table thead th {
    background: #f8f9fc !important;
    border-color: #e3e6f0 !important;
    color: #5a5c69 !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    font-size: 0.8rem !important;
    padding: 15px !important;
}

.table tbody tr {
    border-bottom: 1px solid #e3e6f0 !important;
}

.table tbody tr:hover {
    background: #f8f9fc !important;
}

.table tbody td {
    padding: 12px 15px !important;
    vertical-align: middle !important;
}

/* Forms */
.form-control {
    border: 1px solid #d1d3e2 !important;
    border-radius: 5px !important;
    padding: 10px 12px !important;
    font-size: 0.875rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus {
    border-color: #3498db !important;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25) !important;
}

.form-group label {
    font-weight: 600 !important;
    color: #5a5c69 !important;
    margin-bottom: 5px !important;
}

/* Breadcrumb */
.breadcrumb {
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    font-size: 0.875rem !important;
}

.breadcrumb-item {
    color: #858796 !important;
}

.breadcrumb-item.active {
    color: #5a5c69 !important;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←" !important;
    color: #858796 !important;
}

/* Page Header */
.page-header {
    background: white !important;
    padding: 20px 0 !important;
    border-bottom: 1px solid #e3e6f0 !important;
    margin-bottom: 20px !important;
}

.page-title {
    font-size: 1.75rem !important;
    font-weight: 400 !important;
    color: #5a5c69 !important;
    margin: 0 !important;
}

/* Quick Actions */
.quick-action {
    background: white !important;
    border: 1px solid #e3e6f0 !important;
    border-radius: 8px !important;
    padding: 20px !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    color: #5a5c69 !important;
    display: block !important;
}

.quick-action:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
    text-decoration: none !important;
    color: #5a5c69 !important;
}

.quick-action-icon {
    font-size: 2.5rem !important;
    margin-bottom: 10px !important;
    color: #3498db !important;
}

.quick-action-title {
    font-weight: 600 !important;
    margin: 0 !important;
}

/* Responsive */
@media (max-width: 768px) {
    .main-sidebar {
        transform: translateX(100%) !important;
    }
    
    .sidebar-open .main-sidebar {
        transform: translateX(0) !important;
    }
    
    .content-wrapper,
    .main-header.navbar {
        margin-right: 0 !important;
    }
}

/* Utilities */
.text-primary { color: #3498db !important; }
.text-success { color: #27ae60 !important; }
.text-warning { color: #f39c12 !important; }
.text-danger { color: #e74c3c !important; }
.text-muted { color: #858796 !important; }

.bg-primary { background: #3498db !important; }
.bg-success { background: #27ae60 !important; }
.bg-warning { background: #f39c12 !important; }
.bg-danger { background: #e74c3c !important; }

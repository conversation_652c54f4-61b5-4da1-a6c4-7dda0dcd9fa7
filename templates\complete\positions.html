{% extends "professional_base.html" %}

{% block title %}إدارة الوظائف - نظام إدارة الجمارك الجزائرية{% endblock %}

{% block page_title %}إدارة الوظائف{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item"><a href="{{ url_for('settings') }}">الإعدادات</a></li>
<li class="breadcrumb-item active">الوظائف</li>
{% endblock %}

{% block content %}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-briefcase mr-2"></i>
            إدارة الوظائف والمناصب
        </h1>
        <p class="text-muted">إدارة الوظائف والمسؤوليات في الجمارك الجزائرية</p>
    </div>
    <div class="col-md-4 text-left">
        <button class="btn btn-primary" data-toggle="modal" data-target="#addPositionModal">
            <i class="fas fa-plus mr-1"></i>
            إضافة وظيفة جديدة
        </button>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card primary">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">إجمالي الوظائف</div>
                    <div class="stats-number">{{ positions|length }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-briefcase stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card success">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">الوظائف الإدارية</div>
                    <div class="stats-number">{{ positions|selectattr('name', 'search', 'مدير|رئيس|مكلف')|list|length }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-user-tie stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card warning">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">الوظائف التقنية</div>
                    <div class="stats-number">{{ positions|selectattr('name', 'search', 'مفتش|محاسب|كاتب')|list|length }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-tools stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card info">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">الموظفين المعينين</div>
                    <div class="stats-number">{{ positions|sum(attribute='employees')|length if positions else 0 }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-users stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Positions Grid -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list mr-2"></i>
                    قائمة الوظائف
                </h5>
                <div class="card-tools">
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-tool active" onclick="toggleView('grid')" id="gridViewBtn">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button type="button" class="btn btn-tool" onclick="toggleView('table')" id="tableViewBtn">
                            <i class="fas fa-table"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                
                <!-- Grid View -->
                <div id="gridView" class="row">
                    {% for position in positions %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 position-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h6 class="card-title mb-0">{{ position.name }}</h6>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item" href="#" onclick="editPosition({{ position.id }})">
                                                <i class="fas fa-edit mr-2"></i>تعديل
                                            </a>
                                            <a class="dropdown-item" href="#" onclick="viewEmployees({{ position.id }}, '{{ position.name }}')">
                                                <i class="fas fa-users mr-2"></i>عرض الموظفين
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item text-danger" href="#" onclick="deletePosition({{ position.id }}, '{{ position.name }}')">
                                                <i class="fas fa-trash mr-2"></i>حذف
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <p class="card-text text-muted small">{{ position.description or 'لا يوجد وصف' }}</p>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge badge-primary">{{ position.employees|length }} موظف</span>
                                    <small class="text-muted">
                                        {% if position.name|search('مدير') %}
                                            <i class="fas fa-crown text-warning"></i> إدارية
                                        {% elif position.name|search('مفتش') %}
                                            <i class="fas fa-search text-info"></i> تفتيش
                                        {% elif position.name|search('محاسب') %}
                                            <i class="fas fa-calculator text-success"></i> مالية
                                        {% else %}
                                            <i class="fas fa-briefcase text-secondary"></i> عامة
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Table View (Hidden by default) -->
                <div id="tableView" class="table-responsive" style="display: none;">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الوظيفة</th>
                                <th>الوصف</th>
                                <th>النوع</th>
                                <th>عدد الموظفين</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for position in positions %}
                            <tr>
                                <td>
                                    <strong>{{ position.name }}</strong>
                                </td>
                                <td>
                                    <span class="text-muted">{{ position.description or 'لا يوجد وصف' }}</span>
                                </td>
                                <td>
                                    {% if position.name|search('مدير') %}
                                        <span class="badge badge-warning">إدارية</span>
                                    {% elif position.name|search('مفتش') %}
                                        <span class="badge badge-info">تفتيش</span>
                                    {% elif position.name|search('محاسب') %}
                                        <span class="badge badge-success">مالية</span>
                                    {% else %}
                                        <span class="badge badge-secondary">عامة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge badge-primary">{{ position.employees|length }}</span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewEmployees({{ position.id }}, '{{ position.name }}')" title="عرض الموظفين">
                                            <i class="fas fa-users"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="editPosition({{ position.id }})" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deletePosition({{ position.id }}, '{{ position.name }}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Position Modal -->
<div class="modal fade" id="addPositionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة وظيفة جديدة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addPositionForm">
                    <div class="form-group">
                        <label for="positionName">اسم الوظيفة</label>
                        <input type="text" class="form-control" id="positionName" required>
                        <small class="form-text text-muted">مثال: مكلف بالموارد البشرية</small>
                    </div>
                    <div class="form-group">
                        <label for="positionDescription">الوصف والمسؤوليات</label>
                        <textarea class="form-control" id="positionDescription" rows="4" placeholder="اكتب وصف مفصل للوظيفة والمسؤوليات المطلوبة..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="positionType">نوع الوظيفة</label>
                        <select class="form-control" id="positionType">
                            <option value="">اختر النوع</option>
                            <option value="إدارية">إدارية</option>
                            <option value="تقنية">تقنية</option>
                            <option value="مالية">مالية</option>
                            <option value="تفتيش">تفتيش</option>
                            <option value="عامة">عامة</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="savePosition()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Employees Modal -->
<div class="modal fade" id="employeesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="employeesModalTitle">موظفي الوظيفة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="employeesContent">
                    <!-- سيتم تحميل الموظفين هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// تبديل العرض بين الشبكة والجدول
function toggleView(viewType) {
    if (viewType === 'grid') {
        $('#gridView').show();
        $('#tableView').hide();
        $('#gridViewBtn').addClass('active');
        $('#tableViewBtn').removeClass('active');
    } else {
        $('#gridView').hide();
        $('#tableView').show();
        $('#gridViewBtn').removeClass('active');
        $('#tableViewBtn').addClass('active');
    }
}

function editPosition(positionId) {
    alert('تعديل الوظيفة رقم: ' + positionId);
}

function deletePosition(positionId, positionName) {
    if (confirm(`هل أنت متأكد من حذف الوظيفة: ${positionName}؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        alert('سيتم حذف الوظيفة...');
    }
}

function viewEmployees(positionId, positionName) {
    $('#employeesModalTitle').text(`موظفي وظيفة: ${positionName}`);
    $('#employeesContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>');
    $('#employeesModal').modal('show');
    
    // محاكاة تحميل الموظفين
    setTimeout(function() {
        $('#employeesContent').html(`
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>رقم التسجيل</th>
                            <th>الاسم الكامل</th>
                            <th>المديرية</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>EMP001</td>
                            <td>أحمد بن محمد</td>
                            <td>مديرية الموارد البشرية</td>
                            <td><span class="badge badge-success">نشط</span></td>
                        </tr>
                        <tr>
                            <td>EMP002</td>
                            <td>فاطمة علي</td>
                            <td>المديرية الجهوية - الجزائر</td>
                            <td><span class="badge badge-success">نشط</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="text-center mt-3">
                <a href="/employees?position=${positionId}" class="btn btn-primary btn-sm">
                    <i class="fas fa-external-link-alt mr-1"></i>
                    عرض جميع الموظفين
                </a>
            </div>
        `);
    }, 1000);
}

function savePosition() {
    const name = $('#positionName').val();
    const description = $('#positionDescription').val();
    const type = $('#positionType').val();
    
    if (!name) {
        alert('يرجى إدخال اسم الوظيفة');
        return;
    }
    
    alert('تم حفظ الوظيفة بنجاح');
    $('#addPositionModal').modal('hide');
    location.reload();
}

$(document).ready(function() {
    // تأثيرات تحويم على البطاقات
    $('.position-card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );
    
    // تفعيل tooltips
    $('[title]').tooltip();
});
</script>

<style>
.position-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.position-card:hover {
    transform: translateY(-2px);
    border-color: #007bff;
}

.stats-card {
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-tool.active {
    background-color: #007bff;
    color: white;
}
</style>
{% endblock %}

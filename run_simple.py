from flask import Flask, render_template
from datetime import date

app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-key'

@app.route('/')
def index():
    return render_template('index.html', total_employees=150, active_employees=140)

@app.route('/employees')
def employees_list():
    return render_template('employees/list.html', employees=None, search='')

@app.route('/settings')
def settings():
    return render_template('settings/index.html')

if __name__ == '__main__':
    print("Starting server on http://localhost:5000")
    app.run(debug=True, host='127.0.0.1', port=5000)

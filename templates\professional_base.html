<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}نظام إدارة الجمارك الجزائرية{% endblock %}</title>

    <!-- Google Font: Cairo for Arabic -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Bootstrap 5 RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- AdminLTE 3 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2.0/dist/css/adminlte.min.css">
    <!-- Professional CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/professional.css') }}">
</head>

<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
        <!-- Left navbar links -->
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" data-widget="pushmenu" href="#" role="button">
                    <i class="fas fa-bars"></i>
                </a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="{{ url_for('index') }}" class="nav-link">
                    <i class="fas fa-home ml-1"></i>
                    الرئيسية
                </a>
            </li>
        </ul>

        <!-- Right navbar links -->
        <ul class="navbar-nav mr-auto">
            <!-- User Menu -->
            <li class="nav-item dropdown">
                <a class="nav-link" data-toggle="dropdown" href="#">
                    <i class="fas fa-user-circle fa-lg"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-lg dropdown-menu-left">
                    <span class="dropdown-item dropdown-header">المستخدم</span>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-user ml-2"></i> الملف الشخصي
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-cog ml-2"></i> الإعدادات
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-sign-out-alt ml-2"></i> تسجيل الخروج
                    </a>
                </div>
            </li>
            <!-- Fullscreen -->
            <li class="nav-item">
                <a class="nav-link" data-widget="fullscreen" href="#" role="button">
                    <i class="fas fa-expand-arrows-alt"></i>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Sidebar Container -->
    <aside class="main-sidebar elevation-4">
        <!-- Brand Logo -->
        <a href="{{ url_for('index') }}" class="brand-link">
            <div class="brand-image" style="width: 33px; height: 33px; background: #3498db; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 16px; margin-left: 10px;">ج</div>
            <span class="brand-text">الجمارك الجزائرية</span>
        </a>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Sidebar Menu -->
            <nav class="mt-2">
                <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                    
                    <!-- Dashboard -->
                    <li class="nav-item">
                        <a href="{{ url_for('index') }}" class="nav-link {% if request.endpoint == 'index' %}active{% endif %}">
                            <i class="nav-icon fas fa-tachometer-alt"></i>
                            <p>لوحة التحكم</p>
                        </a>
                    </li>
                    
                    <!-- Employees Management -->
                    <li class="nav-item {% if request.endpoint in ['employees_list', 'add_employee', 'employee_detail'] %}menu-open{% endif %}">
                        <a href="#" class="nav-link {% if request.endpoint in ['employees_list', 'add_employee', 'employee_detail'] %}active{% endif %}">
                            <i class="nav-icon fas fa-users"></i>
                            <p>
                                إدارة الموظفين
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a href="{{ url_for('employees_list') }}" class="nav-link {% if request.endpoint == 'employees_list' %}active{% endif %}">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>قائمة الموظفين</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url_for('add_employee') }}" class="nav-link {% if request.endpoint == 'add_employee' %}active{% endif %}">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>إضافة موظف جديد</p>
                                </a>
                            </li>
                        </ul>
                    </li>
                    
                    <!-- Reports -->
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="nav-icon fas fa-chart-bar"></i>
                            <p>التقارير</p>
                        </a>
                    </li>
                    
                    <!-- Settings -->
                    <li class="nav-item {% if request.endpoint in ['administrative_divisions', 'ranks_settings', 'positions_settings'] %}menu-open{% endif %}">
                        <a href="#" class="nav-link {% if request.endpoint in ['administrative_divisions', 'ranks_settings', 'positions_settings'] %}active{% endif %}">
                            <i class="nav-icon fas fa-cogs"></i>
                            <p>
                                الإعدادات
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a href="{{ url_for('administrative_divisions') }}" class="nav-link {% if request.endpoint == 'administrative_divisions' %}active{% endif %}">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>التقسيم الإداري</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url_for('ranks_settings') }}" class="nav-link {% if request.endpoint == 'ranks_settings' %}active{% endif %}">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>الرتب</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url_for('positions_settings') }}" class="nav-link {% if request.endpoint == 'positions_settings' %}active{% endif %}">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>الوظائف</p>
                                </a>
                            </li>
                        </ul>
                    </li>
                    
                </ul>
            </nav>
        </div>
    </aside>

    <!-- Content Wrapper -->
    <div class="content-wrapper">
        <!-- Content Header -->
        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0">{% block page_title %}{% endblock %}</h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-left">
                            {% block breadcrumb %}
                            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                            {% endblock %}
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                {% block content %}{% endblock %}
                
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="main-footer">
        <strong>حقوق الطبع والنشر &copy; 2024 <a href="#">الجمارك الجزائرية</a>.</strong>
        جميع الحقوق محفوظة.
        <div class="float-left d-none d-sm-inline-block">
            <b>الإصدار</b> 1.0.0
        </div>
    </footer>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- Bootstrap 5 -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE 3 -->
<script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2.0/dist/js/adminlte.min.js"></script>

<!-- Professional JS -->
<script>
$(document).ready(function() {
    // RTL fixes
    $('body').addClass('sidebar-mini layout-fixed');
    
    // Sidebar toggle fix for RTL
    $('[data-widget="pushmenu"]').on('click', function() {
        setTimeout(function() {
            $(window).trigger('resize');
        }, 300);
    });
    
    // Auto-hide alerts after 5 seconds
    $('.alert').delay(5000).fadeOut();
    
    // Add loading state to buttons
    $('.btn').on('click', function() {
        var btn = $(this);
        if (!btn.hasClass('no-loading')) {
            btn.prop('disabled', true);
            var originalText = btn.html();
            btn.html('<i class="fas fa-spinner fa-spin ml-1"></i> جاري التحميل...');
            
            setTimeout(function() {
                btn.prop('disabled', false);
                btn.html(originalText);
            }, 2000);
        }
    });
});
</script>

{% block extra_js %}{% endblock %}

</body>
</html>

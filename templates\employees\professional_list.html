{% extends "professional_base.html" %}

{% block title %}قائمة الموظفين - نظام إدارة الجمارك الجزائرية{% endblock %}

{% block page_title %}قائمة الموظفين{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item">إدارة الموظفين</li>
<li class="breadcrumb-item active">قائمة الموظفين</li>
{% endblock %}

{% block content %}

<!-- Page Header -->
<div class="page-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-users ml-2"></i>
                    قائمة الموظفين
                </h1>
            </div>
            <div class="col-md-6 text-left">
                <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
                    <i class="fas fa-plus ml-1"></i>
                    إضافة موظف جديد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-search ml-2"></i>
                البحث والتصفية
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('employees_list') }}">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="search">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ search }}" placeholder="البحث بالاسم أو رقم التسجيل">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="status">الحالة</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="نشط" {% if status_filter == 'نشط' %}selected{% endif %}>نشط</option>
                                    <option value="في إجازة" {% if status_filter == 'في إجازة' %}selected{% endif %}>في إجازة</option>
                                    <option value="غير نشط" {% if status_filter == 'غير نشط' %}selected{% endif %}>غير نشط</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="department">القسم</label>
                                <select class="form-control" id="department" name="department">
                                    <option value="">جميع الأقسام</option>
                                    <option value="الموارد البشرية">الموارد البشرية</option>
                                    <option value="المالية">المالية</option>
                                    <option value="التفتيش">التفتيش</option>
                                    <option value="الإدارة">الإدارة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-search ml-1"></i>
                                        بحث
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card primary">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">إجمالي الموظفين</div>
                    <div class="stats-number">{{ employees.total }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-users stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card success">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">النشطين</div>
                    <div class="stats-number">{{ employees.items | selectattr('status', 'equalto', 'نشط') | list | length }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-user-check stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card warning">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">في إجازة</div>
                    <div class="stats-number">{{ employees.items | selectattr('status', 'equalto', 'في إجازة') | list | length }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-calendar-times stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card danger">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">غير نشط</div>
                    <div class="stats-number">{{ employees.items | selectattr('status', 'equalto', 'غير نشط') | list | length }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-user-times stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Employees Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-table ml-2"></i>
                قائمة الموظفين
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if employees.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>رقم التسجيل</th>
                                <th>الاسم الكامل</th>
                                <th>الرتبة</th>
                                <th>الوظيفة</th>
                                <th>القسم</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees.items %}
                            <tr>
                                <td>
                                    {% if employee.photo %}
                                        <img src="{{ url_for('static', filename='uploads/' + employee.photo) }}" 
                                             class="img-circle" width="40" height="40" alt="صورة الموظف">
                                    {% else %}
                                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px; font-weight: bold;">
                                            {{ employee.first_name[0] }}{{ employee.last_name[0] }}
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ employee.registration_number }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ employee.full_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ employee.gender }} - {{ employee.age }} سنة</small>
                                    </div>
                                </td>
                                <td>{{ employee.current_rank.name }}</td>
                                <td>{{ employee.current_position.name }}</td>
                                <td>{{ employee.department }}</td>
                                <td>
                                    {% if employee.status == 'نشط' %}
                                        <span class="badge badge-success">{{ employee.status }}</span>
                                    {% elif employee.status == 'في إجازة' %}
                                        <span class="badge badge-warning">{{ employee.status }}</span>
                                    {% else %}
                                        <span class="badge badge-danger">{{ employee.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('employee_detail', id=employee.id) }}" 
                                           class="btn btn-sm btn-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" title="حذف"
                                                onclick="confirmDelete('{{ employee.full_name }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if employees.pages > 1 %}
                <nav aria-label="تنقل الصفحات">
                    <ul class="pagination justify-content-center">
                        {% if employees.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees_list', page=employees.page-1, search=search, status=status_filter) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in range(1, employees.pages + 1) %}
                            {% if page_num == employees.page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                            {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('employees_list', page=page_num, search=search, status=status_filter) }}">{{ page_num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if employees.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees_list', page=employees.page+1, search=search, status=status_filter) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد موظفين</h5>
                    <p class="text-muted">لم يتم العثور على أي موظفين بالمعايير المحددة</p>
                    <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
                        <i class="fas fa-plus ml-1"></i>
                        إضافة موظف جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(employeeName) {
    if (confirm('هل أنت متأكد من حذف الموظف: ' + employeeName + '؟')) {
        // هنا يمكن إضافة كود الحذف
        alert('تم حذف الموظف بنجاح');
    }
}

$(document).ready(function() {
    // Auto-submit form on filter change
    $('#status, #department').change(function() {
        $(this).closest('form').submit();
    });
    
    // Clear search
    if ($('#search').val()) {
        $('#search').after('<button type="button" class="btn btn-outline-secondary btn-sm mr-2" onclick="clearSearch()">مسح</button>');
    }
});

function clearSearch() {
    $('#search').val('');
    $('#status').val('');
    $('#department').val('');
    $('form').submit();
}
</script>
{% endblock %}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الجمارك الجزائرية - النسخة المتكاملة
Complete Algerian Customs Management System
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, DateField, TextAreaField, IntegerField, FileField, BooleanField
from wtforms.validators import DataRequired, Email, Length, Optional, NumberRange
from werkzeug.utils import secure_filename
from datetime import date, datetime
import os

print("=" * 80)
print("🇩🇿 نظام إدارة الجمارك الجزائرية - النسخة المتكاملة")
print("   Complete Algerian Customs Management System")
print("=" * 80)

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'complete-customs-management-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///customs_management.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)
migrate = Migrate(app, db)

# إنشاء مجلدات الرفع
for folder in ['static/uploads', 'static/documents', 'static/photos']:
    os.makedirs(folder, exist_ok=True)

# ===== النماذج (Models) =====

class Wilaya(db.Model):
    __tablename__ = 'wilayas'
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(2), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_fr = db.Column(db.String(100), nullable=False)
    communes = db.relationship('Commune', backref='wilaya', lazy=True)

class Commune(db.Model):
    __tablename__ = 'communes'
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(4), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_fr = db.Column(db.String(100), nullable=False)
    wilaya_id = db.Column(db.Integer, db.ForeignKey('wilayas.id'), nullable=False)

class Corps(db.Model):
    __tablename__ = 'corps'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    employees = db.relationship('Employee', backref='corps', lazy=True)

class Rank(db.Model):
    __tablename__ = 'ranks'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    level = db.Column(db.Integer, nullable=False)
    corps_id = db.Column(db.Integer, db.ForeignKey('corps.id'), nullable=False)
    employees = db.relationship('Employee', backref='current_rank', lazy=True)

class Position(db.Model):
    __tablename__ = 'positions'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    employees = db.relationship('Employee', backref='current_position', lazy=True)

class Directorate(db.Model):
    __tablename__ = 'directorates'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(10), unique=True, nullable=False)
    address = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    employees = db.relationship('Employee', backref='directorate', lazy=True)

class Service(db.Model):
    __tablename__ = 'services'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    directorate_id = db.Column(db.Integer, db.ForeignKey('directorates.id'), nullable=False)
    employees = db.relationship('Employee', backref='service', lazy=True)

class Employee(db.Model):
    __tablename__ = 'employees'
    id = db.Column(db.Integer, primary_key=True)
    registration_number = db.Column(db.String(20), unique=True, nullable=False)
    
    # Personal Information
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    first_name_fr = db.Column(db.String(50))
    last_name_fr = db.Column(db.String(50))
    gender = db.Column(db.String(10), nullable=False)
    birth_date = db.Column(db.Date, nullable=False)
    birth_place = db.Column(db.String(100))
    birth_wilaya_id = db.Column(db.Integer, db.ForeignKey('wilayas.id'))
    birth_commune_id = db.Column(db.Integer, db.ForeignKey('communes.id'))
    
    # Contact Information
    phone1 = db.Column(db.String(20))
    phone2 = db.Column(db.String(20))
    email = db.Column(db.String(100))
    primary_address = db.Column(db.Text)
    secondary_address = db.Column(db.Text)
    
    # Family Information
    marital_status = db.Column(db.String(20))
    children_count = db.Column(db.Integer, default=0)
    dependents_count = db.Column(db.Integer, default=0)
    
    # Health Information
    blood_type = db.Column(db.String(5))
    
    # Official Information
    social_security_number = db.Column(db.String(20))
    national_id_number = db.Column(db.String(20))
    
    # Employment Information
    hiring_date = db.Column(db.Date, nullable=False)
    corps_id = db.Column(db.Integer, db.ForeignKey('corps.id'), nullable=False)
    rank_id = db.Column(db.Integer, db.ForeignKey('ranks.id'), nullable=False)
    position_id = db.Column(db.Integer, db.ForeignKey('positions.id'), nullable=False)
    directorate_id = db.Column(db.Integer, db.ForeignKey('directorates.id'), nullable=False)
    service_id = db.Column(db.Integer, db.ForeignKey('services.id'))
    
    # Status
    status = db.Column(db.String(20), default='نشط')
    
    # Files
    photo = db.Column(db.String(100))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    birth_wilaya = db.relationship('Wilaya', foreign_keys=[birth_wilaya_id])
    birth_commune = db.relationship('Commune', foreign_keys=[birth_commune_id])
    spouses = db.relationship('Spouse', backref='employee', lazy=True)
    children = db.relationship('Child', backref='employee', lazy=True)
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_name_fr(self):
        if self.first_name_fr and self.last_name_fr:
            return f"{self.first_name_fr} {self.last_name_fr}"
        return self.full_name
    
    @property
    def age(self):
        today = date.today()
        return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))

class Spouse(db.Model):
    __tablename__ = 'spouses'
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    birth_date = db.Column(db.Date)
    profession = db.Column(db.String(100))
    national_id = db.Column(db.String(20))

class Child(db.Model):
    __tablename__ = 'children'
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    birth_date = db.Column(db.Date, nullable=False)
    gender = db.Column(db.String(10), nullable=False)
    education_level = db.Column(db.String(50))

# ===== النماذج (Forms) =====

class EmployeeForm(FlaskForm):
    # Personal Information
    registration_number = StringField('رقم التسجيل', validators=[DataRequired(), Length(min=3, max=20)])
    first_name = StringField('الاسم', validators=[DataRequired(), Length(min=2, max=50)])
    last_name = StringField('اللقب', validators=[DataRequired(), Length(min=2, max=50)])
    first_name_fr = StringField('الاسم بالفرنسية', validators=[Optional()])
    last_name_fr = StringField('اللقب بالفرنسية', validators=[Optional()])
    gender = SelectField('الجنس', choices=[('ذكر', 'ذكر'), ('أنثى', 'أنثى')], validators=[DataRequired()])
    birth_date = DateField('تاريخ الميلاد', validators=[DataRequired()])
    birth_place = StringField('مكان الميلاد', validators=[Optional()])
    birth_wilaya_id = SelectField('ولاية الميلاد', coerce=int, validators=[Optional()])
    birth_commune_id = SelectField('بلدية الميلاد', coerce=int, validators=[Optional()])
    
    # Contact Information
    phone1 = StringField('الهاتف الأول', validators=[Optional(), Length(max=20)])
    phone2 = StringField('الهاتف الثاني', validators=[Optional(), Length(max=20)])
    email = StringField('البريد الإلكتروني', validators=[Optional(), Email(), Length(max=100)])
    primary_address = TextAreaField('العنوان الأساسي', validators=[Optional()])
    secondary_address = TextAreaField('العنوان الثانوي', validators=[Optional()])
    
    # Family Information
    marital_status = SelectField('الحالة المدنية', 
                               choices=[('أعزب', 'أعزب'), ('متزوج', 'متزوج'), ('مطلق', 'مطلق'), ('أرمل', 'أرمل')])
    children_count = IntegerField('عدد الأطفال', default=0)
    dependents_count = IntegerField('عدد المعالين', default=0)
    
    # Health Information
    blood_type = SelectField('فصيلة الدم', 
                           choices=[('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'), 
                                  ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-')])
    
    # Official Information
    social_security_number = StringField('رقم الضمان الاجتماعي')
    national_id_number = StringField('رقم البطاقة الوطنية')
    
    # Employment Information
    hiring_date = DateField('تاريخ التوظيف', validators=[DataRequired()])
    corps_id = SelectField('السلك', coerce=int, validators=[DataRequired()])
    rank_id = SelectField('الرتبة', coerce=int, validators=[DataRequired()])
    position_id = SelectField('الوظيفة', coerce=int, validators=[DataRequired()])
    directorate_id = SelectField('المديرية', coerce=int, validators=[DataRequired()])
    service_id = SelectField('المصلحة', coerce=int)
    
    # Status
    status = SelectField('الحالة',
                        choices=[('نشط', 'نشط'), ('في إجازة', 'في إجازة'), ('معلق', 'معلق'), ('متقاعد', 'متقاعد')],
                        default='نشط')

    # Files
    photo = FileField('صورة الموظف')

class SpouseForm(FlaskForm):
    """نموذج إضافة الزوج/الزوجة"""
    first_name = StringField('الاسم', validators=[DataRequired(), Length(min=2, max=50)])
    last_name = StringField('اللقب', validators=[DataRequired(), Length(min=2, max=50)])
    birth_date = DateField('تاريخ الميلاد', validators=[Optional()])
    profession = StringField('المهنة', validators=[Optional(), Length(max=100)])
    workplace = StringField('مكان العمل', validators=[Optional(), Length(max=100)])

class ChildForm(FlaskForm):
    """نموذج إضافة الطفل"""
    first_name = StringField('الاسم', validators=[DataRequired(), Length(min=2, max=50)])
    last_name = StringField('اللقب', validators=[DataRequired(), Length(min=2, max=50)])
    birth_date = DateField('تاريخ الميلاد', validators=[DataRequired()])
    gender = SelectField('الجنس', choices=[('ذكر', 'ذكر'), ('أنثى', 'أنثى')], validators=[DataRequired()])
    education_level = StringField('المستوى التعليمي', validators=[Optional(), Length(max=50)])

# ===== الدوال المساعدة =====

def init_database():
    """تهيئة قاعدة البيانات بالبيانات الأساسية"""
    with app.app_context():
        db.create_all()
        
        # إضافة الولايات
        if not Wilaya.query.first():
            wilayas_data = [
                ('01', 'أدرار', 'Adrar'),
                ('02', 'الشلف', 'Chlef'),
                ('03', 'الأغواط', 'Laghouat'),
                ('04', 'أم البواقي', 'Oum El Bouaghi'),
                ('05', 'باتنة', 'Batna'),
                ('06', 'بجاية', 'Béjaïa'),
                ('07', 'بسكرة', 'Biskra'),
                ('08', 'بشار', 'Béchar'),
                ('09', 'البليدة', 'Blida'),
                ('10', 'البويرة', 'Bouira'),
                ('11', 'تمنراست', 'Tamanrasset'),
                ('12', 'تبسة', 'Tébessa'),
                ('13', 'تلمسان', 'Tlemcen'),
                ('14', 'تيارت', 'Tiaret'),
                ('15', 'تيزي وزو', 'Tizi Ouzou'),
                ('16', 'الجزائر', 'Alger'),
                ('17', 'الجلفة', 'Djelfa'),
                ('18', 'جيجل', 'Jijel'),
                ('19', 'سطيف', 'Sétif'),
                ('20', 'سعيدة', 'Saïda'),
                ('21', 'سكيكدة', 'Skikda'),
                ('22', 'سيدي بلعباس', 'Sidi Bel Abbès'),
                ('23', 'عنابة', 'Annaba'),
                ('24', 'قالمة', 'Guelma'),
                ('25', 'قسنطينة', 'Constantine'),
                ('26', 'المدية', 'Médéa'),
                ('27', 'مستغانم', 'Mostaganem'),
                ('28', 'المسيلة', 'M\'Sila'),
                ('29', 'معسكر', 'Mascara'),
                ('30', 'ورقلة', 'Ouargla'),
                ('31', 'وهران', 'Oran'),
                ('32', 'البيض', 'El Bayadh'),
                ('33', 'إليزي', 'Illizi'),
                ('34', 'برج بوعريريج', 'Bordj Bou Arréridj'),
                ('35', 'بومرداس', 'Boumerdès'),
                ('36', 'الطارف', 'El Tarf'),
                ('37', 'تندوف', 'Tindouf'),
                ('38', 'تيسمسيلت', 'Tissemsilt'),
                ('39', 'الوادي', 'El Oued'),
                ('40', 'خنشلة', 'Khenchela'),
                ('41', 'سوق أهراس', 'Souk Ahras'),
                ('42', 'تيبازة', 'Tipaza'),
                ('43', 'ميلة', 'Mila'),
                ('44', 'عين الدفلى', 'Aïn Defla'),
                ('45', 'النعامة', 'Naâma'),
                ('46', 'عين تموشنت', 'Aïn Témouchent'),
                ('47', 'غرداية', 'Ghardaïa'),
                ('48', 'غليزان', 'Relizane'),
                ('49', 'تيميمون', 'Timimoun'),
                ('50', 'برج باجي مختار', 'Bordj Badji Mokhtar'),
                ('51', 'أولاد جلال', 'Ouled Djellal'),
                ('52', 'بني عباس', 'Béni Abbès'),
                ('53', 'عين صالح', 'In Salah'),
                ('54', 'عين قزام', 'In Guezzam'),
                ('55', 'تقرت', 'Touggourt'),
                ('56', 'جانت', 'Djanet'),
                ('57', 'المغير', 'El M\'Ghair'),
                ('58', 'المنيعة', 'El Meniaa')
            ]
            
            for code, name_ar, name_fr in wilayas_data:
                wilaya = Wilaya(code=code, name_ar=name_ar, name_fr=name_fr)
                db.session.add(wilaya)
            
            db.session.commit()
            print("✅ تم إضافة الولايات")

        # إضافة الأسلاك
        if not Corps.query.first():
            corps_data = [
                ('أسلاك خاصة', 'الأسلاك الخاصة بالجمارك'),
                ('أسلاك مشتركة', 'الأسلاك المشتركة مع الإدارات الأخرى'),
                ('أسلاك تقنية', 'الأسلاك التقنية والمتخصصة'),
            ]

            for name, description in corps_data:
                corps = Corps(name=name, description=description)
                db.session.add(corps)

            db.session.commit()
            print("✅ تم إضافة الأسلاك")

        # إضافة الرتب
        if not Rank.query.first():
            ranks_data = [
                ('عون جمارك', 1, 1),
                ('عون جمارك رئيسي', 2, 1),
                ('مفتش للجمارك', 3, 1),
                ('مفتش رئيسي للجمارك', 4, 1),
                ('مفتش مركزي للجمارك', 5, 1),
                ('مدير دراسات', 6, 1),
                ('مدير دراسات رئيسي', 7, 1),
                ('مدير دراسات مركزي', 8, 1),
            ]

            for name, level, corps_id in ranks_data:
                rank = Rank(name=name, level=level, corps_id=corps_id)
                db.session.add(rank)

            db.session.commit()
            print("✅ تم إضافة الرتب")

        # إضافة الوظائف
        if not Position.query.first():
            positions_data = [
                ('مكلف بالموارد البشرية', 'إدارة شؤون الموظفين'),
                ('مكلف بالمالية والمحاسبة', 'إدارة الشؤون المالية'),
                ('مكلف بالتفتيش', 'أعمال التفتيش والمراقبة'),
                ('مكلف بالتكوين', 'التكوين والتدريب'),
                ('مكلف بالإحصائيات', 'جمع وتحليل الإحصائيات'),
                ('رئيس مكتب', 'إدارة مكتب'),
                ('رئيس مصلحة', 'إدارة مصلحة'),
                ('رئيس قسم', 'إدارة قسم'),
                ('مدير', 'إدارة مديرية'),
                ('كاتب', 'أعمال كتابية'),
                ('محاسب', 'أعمال محاسبية'),
                ('مفتش ميداني', 'التفتيش الميداني'),
            ]

            for name, description in positions_data:
                position = Position(name=name, description=description)
                db.session.add(position)

            db.session.commit()
            print("✅ تم إضافة الوظائف")

        # إضافة المديريات
        if not Directorate.query.first():
            directorates_data = [
                ('المديرية العامة للجمارك', 'DGD', 'الجزائر العاصمة', '021-XX-XX-XX'),
                ('المديرية الجهوية للجمارك - الجزائر', 'DRD-ALG', 'الجزائر', '021-XX-XX-XX'),
                ('المديرية الجهوية للجمارك - وهران', 'DRD-ORA', 'وهران', '041-XX-XX-XX'),
                ('المديرية الجهوية للجمارك - قسنطينة', 'DRD-CST', 'قسنطينة', '031-XX-XX-XX'),
                ('المديرية الجهوية للجمارك - ورقلة', 'DRD-OUA', 'ورقلة', '029-XX-XX-XX'),
                ('مديرية الموارد البشرية', 'DRH', 'الجزائر العاصمة', '021-XX-XX-XX'),
                ('مديرية المالية والمحاسبة', 'DFC', 'الجزائر العاصمة', '021-XX-XX-XX'),
                ('مديرية التكوين', 'DF', 'الجزائر العاصمة', '021-XX-XX-XX'),
            ]

            for name, code, address, phone in directorates_data:
                directorate = Directorate(name=name, code=code, address=address, phone=phone)
                db.session.add(directorate)

            db.session.commit()
            print("✅ تم إضافة المديريات")

        # إضافة المصالح
        if not Service.query.first():
            services_data = [
                ('مصلحة تسيير المستخدمين', 1),
                ('مصلحة التكوين والتطوير', 1),
                ('مصلحة الأجور والمنح', 1),
                ('مصلحة المحاسبة العامة', 2),
                ('مصلحة الميزانية', 2),
                ('مصلحة المراقبة المالية', 2),
                ('مصلحة التفتيش الداخلي', 3),
                ('مصلحة التفتيش الخارجي', 3),
                ('مصلحة المراقبة', 3),
            ]

            for name, directorate_id in services_data:
                service = Service(name=name, directorate_id=directorate_id)
                db.session.add(service)

            db.session.commit()
            print("✅ تم إضافة المصالح")

        # إضافة موظفين تجريبيين
        if not Employee.query.first():
            sample_employees = [
                {
                    'registration_number': 'EMP001',
                    'first_name': 'أحمد',
                    'last_name': 'بن محمد',
                    'first_name_fr': 'Ahmed',
                    'last_name_fr': 'Ben Mohamed',
                    'gender': 'ذكر',
                    'birth_date': datetime(1985, 5, 15).date(),
                    'birth_place': 'الجزائر العاصمة',
                    'birth_wilaya_id': 1,
                    'phone1': '0555123456',
                    'email': '<EMAIL>',
                    'primary_address': 'حي السلام، الجزائر العاصمة',
                    'marital_status': 'متزوج',
                    'children_count': 2,
                    'blood_type': 'A+',
                    'social_security_number': '1850515123456',
                    'national_id_number': '123456789012',
                    'hiring_date': datetime(2010, 9, 1).date(),
                    'corps_id': 1,
                    'rank_id': 3,
                    'position_id': 1,
                    'directorate_id': 1,
                    'service_id': 1,
                    'status': 'نشط'
                },
                {
                    'registration_number': 'EMP002',
                    'first_name': 'فاطمة',
                    'last_name': 'علي',
                    'first_name_fr': 'Fatima',
                    'last_name_fr': 'Ali',
                    'gender': 'أنثى',
                    'birth_date': datetime(1990, 3, 20).date(),
                    'birth_place': 'وهران',
                    'birth_wilaya_id': 2,
                    'phone1': '0666789123',
                    'email': '<EMAIL>',
                    'primary_address': 'حي الأندلس، وهران',
                    'marital_status': 'أعزب',
                    'children_count': 0,
                    'blood_type': 'O+',
                    'social_security_number': '1900320654321',
                    'national_id_number': '987654321098',
                    'hiring_date': datetime(2015, 2, 15).date(),
                    'corps_id': 1,
                    'rank_id': 2,
                    'position_id': 2,
                    'directorate_id': 2,
                    'service_id': 2,
                    'status': 'نشط'
                },
                {
                    'registration_number': 'EMP003',
                    'first_name': 'محمد',
                    'last_name': 'حسن',
                    'first_name_fr': 'Mohamed',
                    'last_name_fr': 'Hassan',
                    'gender': 'ذكر',
                    'birth_date': datetime(1982, 12, 10).date(),
                    'birth_place': 'قسنطينة',
                    'birth_wilaya_id': 3,
                    'phone1': '0777456789',
                    'email': '<EMAIL>',
                    'primary_address': 'حي بودراع صالح، قسنطينة',
                    'marital_status': 'متزوج',
                    'children_count': 3,
                    'blood_type': 'B+',
                    'social_security_number': '1821210987654',
                    'national_id_number': '456789123456',
                    'hiring_date': datetime(2008, 6, 1).date(),
                    'corps_id': 1,
                    'rank_id': 4,
                    'position_id': 6,
                    'directorate_id': 3,
                    'service_id': 7,
                    'status': 'في إجازة'
                }
            ]

            for emp_data in sample_employees:
                employee = Employee(**emp_data)
                db.session.add(employee)

            db.session.commit()
            print("✅ تم إضافة الموظفين التجريبيين")

# ===== الروتات (Routes) =====

@app.route('/')
def index():
    """الصفحة الرئيسية - لوحة التحكم"""
    total_employees = Employee.query.count()
    active_employees = Employee.query.filter_by(status='نشط').count()
    total_directorates = Directorate.query.count()
    total_services = Service.query.count()

    # الموظفين الجدد (آخر 30 يوم)
    from datetime import timedelta
    thirty_days_ago = datetime.now() - timedelta(days=30)
    new_employees = Employee.query.filter(Employee.created_at >= thirty_days_ago).count()

    # إحصائيات حسب الجنس
    male_count = Employee.query.filter_by(gender='ذكر').count()
    female_count = Employee.query.filter_by(gender='أنثى').count()

    # إحصائيات حسب الحالة
    status_stats = db.session.query(Employee.status, db.func.count(Employee.id)).group_by(Employee.status).all()

    return render_template('complete/dashboard.html',
                         total_employees=total_employees,
                         active_employees=active_employees,
                         total_directorates=total_directorates,
                         total_services=total_services,
                         new_employees=new_employees,
                         male_count=male_count,
                         female_count=female_count,
                         status_stats=status_stats)

@app.route('/employees')
def employees_list():
    """قائمة الموظفين مع البحث والتصفية"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status_filter = request.args.get('status', '')
    directorate_filter = request.args.get('directorate', '', type=int)
    gender_filter = request.args.get('gender', '')

    query = Employee.query

    # تطبيق البحث
    if search:
        query = query.filter(
            db.or_(
                Employee.first_name.contains(search),
                Employee.last_name.contains(search),
                Employee.registration_number.contains(search),
                Employee.email.contains(search)
            )
        )

    # تطبيق الفلاتر
    if status_filter:
        query = query.filter_by(status=status_filter)

    if directorate_filter:
        query = query.filter_by(directorate_id=directorate_filter)

    if gender_filter:
        query = query.filter_by(gender=gender_filter)

    # ترتيب النتائج
    query = query.order_by(Employee.created_at.desc())

    # تقسيم الصفحات
    employees = query.paginate(
        page=page, per_page=20, error_out=False
    )

    # جلب البيانات للفلاتر
    directorates = Directorate.query.all()

    return render_template('complete/employees_list.html',
                         employees=employees,
                         search=search,
                         status_filter=status_filter,
                         directorate_filter=directorate_filter,
                         gender_filter=gender_filter,
                         directorates=directorates)

@app.route('/employee/add', methods=['GET', 'POST'])
def add_employee():
    """إضافة موظف جديد"""
    form = EmployeeForm()

    # تحديث خيارات النماذج
    form.birth_wilaya_id.choices = [(0, 'اختر الولاية')] + [(w.id, w.name_ar) for w in Wilaya.query.all()]
    form.birth_commune_id.choices = [(0, 'اختر البلدية')]
    form.corps_id.choices = [(c.id, c.name) for c in Corps.query.all()]
    form.rank_id.choices = [(r.id, r.name) for r in Rank.query.all()]
    form.position_id.choices = [(p.id, p.name) for p in Position.query.all()]
    form.directorate_id.choices = [(d.id, d.name) for d in Directorate.query.all()]
    form.service_id.choices = [(0, 'اختر المصلحة')] + [(s.id, s.name) for s in Service.query.all()]

    if form.validate_on_submit():
        # التحقق من عدم تكرار رقم التسجيل
        existing_employee = Employee.query.filter_by(registration_number=form.registration_number.data).first()
        if existing_employee:
            flash('رقم التسجيل موجود مسبقاً', 'error')
            return render_template('complete/employee_form.html', form=form, title='إضافة موظف جديد')

        # معالجة رفع الصورة
        photo_filename = None
        if form.photo.data:
            photo_file = form.photo.data
            if photo_file.filename:
                photo_filename = secure_filename(f"{form.registration_number.data}_{photo_file.filename}")
                photo_path = os.path.join('static/photos', photo_filename)
                os.makedirs('static/photos', exist_ok=True)
                photo_file.save(photo_path)

        # إنشاء موظف جديد
        employee = Employee(
            registration_number=form.registration_number.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            first_name_fr=form.first_name_fr.data,
            last_name_fr=form.last_name_fr.data,
            gender=form.gender.data,
            birth_date=form.birth_date.data,
            birth_place=form.birth_place.data,
            birth_wilaya_id=form.birth_wilaya_id.data if form.birth_wilaya_id.data else None,
            birth_commune_id=form.birth_commune_id.data if form.birth_commune_id.data else None,
            phone1=form.phone1.data,
            phone2=form.phone2.data,
            email=form.email.data,
            primary_address=form.primary_address.data,
            secondary_address=form.secondary_address.data,
            marital_status=form.marital_status.data,
            children_count=form.children_count.data,
            dependents_count=form.dependents_count.data,
            blood_type=form.blood_type.data,
            social_security_number=form.social_security_number.data,
            national_id_number=form.national_id_number.data,
            hiring_date=form.hiring_date.data,
            corps_id=form.corps_id.data,
            rank_id=form.rank_id.data,
            position_id=form.position_id.data,
            directorate_id=form.directorate_id.data,
            service_id=form.service_id.data if form.service_id.data else None,
            status=form.status.data,
            photo=photo_filename
        )

        db.session.add(employee)
        db.session.commit()

        flash('تم إضافة الموظف بنجاح', 'success')
        return redirect(url_for('employee_detail', id=employee.id))

    return render_template('complete/employee_form.html', form=form, title='إضافة موظف جديد')

@app.route('/employee/<int:id>')
def employee_detail(id):
    """عرض تفاصيل الموظف"""
    employee = Employee.query.get_or_404(id)
    return render_template('complete/employee_detail.html', employee=employee)

@app.route('/employee/<int:id>/edit', methods=['GET', 'POST'])
def edit_employee(id):
    """تعديل بيانات الموظف"""
    employee = Employee.query.get_or_404(id)
    form = EmployeeForm(obj=employee)

    # تحديث خيارات النماذج
    form.birth_wilaya_id.choices = [(0, 'اختر الولاية')] + [(w.id, w.name_ar) for w in Wilaya.query.all()]
    form.birth_commune_id.choices = [(0, 'اختر البلدية')]
    form.corps_id.choices = [(c.id, c.name) for c in Corps.query.all()]
    form.rank_id.choices = [(r.id, r.name) for r in Rank.query.all()]
    form.position_id.choices = [(p.id, p.name) for p in Position.query.all()]
    form.directorate_id.choices = [(d.id, d.name) for d in Directorate.query.all()]
    form.service_id.choices = [(0, 'اختر المصلحة')] + [(s.id, s.name) for s in Service.query.all()]

    if form.validate_on_submit():
        # التحقق من عدم تكرار رقم التسجيل
        existing_employee = Employee.query.filter(
            Employee.registration_number == form.registration_number.data,
            Employee.id != id
        ).first()

        if existing_employee:
            flash('رقم التسجيل موجود مسبقاً', 'error')
            return render_template('complete/employee_form.html', form=form, title='تعديل بيانات الموظف', employee=employee)

        # تحديث البيانات
        form.populate_obj(employee)
        employee.updated_at = datetime.utcnow()

        db.session.commit()
        flash('تم تحديث بيانات الموظف بنجاح', 'success')
        return redirect(url_for('employee_detail', id=employee.id))

    return render_template('complete/employee_form.html', form=form, title='تعديل بيانات الموظف', employee=employee)

@app.route('/settings')
def settings():
    """صفحة الإعدادات الرئيسية"""
    return render_template('complete/settings.html')

@app.route('/settings/administrative_divisions')
def administrative_divisions():
    """إدارة التقسيم الإداري"""
    wilayas = Wilaya.query.all()
    return render_template('complete/administrative_divisions.html', wilayas=wilayas)

@app.route('/settings/ranks')
def ranks_settings():
    """إدارة الرتب"""
    ranks = Rank.query.all()
    corps = Corps.query.all()
    return render_template('complete/ranks.html', ranks=ranks, corps=corps)

@app.route('/settings/positions')
def positions_settings():
    """إدارة الوظائف"""
    positions = Position.query.all()
    return render_template('complete/positions.html', positions=positions)

@app.route('/api/communes/<int:wilaya_id>')
def get_communes(wilaya_id):
    """API لجلب البلديات حسب الولاية"""
    communes = Commune.query.filter_by(wilaya_id=wilaya_id).all()
    return jsonify([{'id': c.id, 'name': c.name_ar} for c in communes])

@app.route('/api/services/<int:directorate_id>')
def get_services(directorate_id):
    """API لجلب المصالح حسب المديرية"""
    services = Service.query.filter_by(directorate_id=directorate_id).all()
    return jsonify([{'id': s.id, 'name': s.name} for s in services])

@app.route('/employee/<int:id>/delete', methods=['POST'])
def delete_employee(id):
    """حذف موظف"""
    employee = Employee.query.get_or_404(id)

    try:
        # حذف السجلات المرتبطة
        Spouse.query.filter_by(employee_id=id).delete()
        Child.query.filter_by(employee_id=id).delete()

        # حذف الموظف
        db.session.delete(employee)
        db.session.commit()

        flash('تم حذف الموظف بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في حذف الموظف: {str(e)}', 'error')

    return redirect(url_for('employees_list'))

@app.route('/reports')
def reports():
    """صفحة التقارير"""
    return render_template('complete/reports.html')

# Context processors
@app.context_processor
def utility_processor():
    """إضافة دوال مساعدة للقوالب"""
    def format_date(date_obj):
        if date_obj:
            return date_obj.strftime('%d/%m/%Y')
        return ''

    def format_datetime(datetime_obj):
        if datetime_obj:
            return datetime_obj.strftime('%d/%m/%Y %H:%M')
        return ''

    def current_year():
        return datetime.now().year

    def moment():
        return datetime.now()

    return dict(
        format_date=format_date,
        format_datetime=format_datetime,
        current_year=current_year,
        moment=moment,
        datetime=datetime
    )

if __name__ == '__main__':
    init_database()
    print("\n" + "=" * 80)
    print("🎯 بدء تشغيل النظام المتكامل...")
    print("📍 العنوان: http://localhost:5000")
    print("🔧 للإيقاف: اضغط Ctrl+C")
    print("=" * 80)
    print("✅ النظام المتكامل جاهز!")
    print()

    try:
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
    finally:
        print("👋 شكراً لاستخدام نظام إدارة الجمارك الجزائرية")

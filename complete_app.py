#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الجمارك الجزائرية - النسخة المتكاملة
Complete Algerian Customs Management System
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, DateField, TextAreaField, IntegerField, FileField, BooleanField
from wtforms.validators import DataRequired, Email, Length, Optional, NumberRange, ValidationError
from werkzeug.utils import secure_filename
import re
from datetime import date, datetime
import os

print("=" * 80)
print("🇩🇿 نظام إدارة الجمارك الجزائرية - النسخة المتكاملة")
print("   Complete Algerian Customs Management System")
print("=" * 80)

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'complete-customs-management-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///customs_management.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)
migrate = Migrate(app, db)

# إنشاء مجلدات الرفع
for folder in ['static/uploads', 'static/documents', 'static/photos']:
    os.makedirs(folder, exist_ok=True)

# ===== النماذج (Models) =====

class Wilaya(db.Model):
    __tablename__ = 'wilayas'
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(2), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_fr = db.Column(db.String(100), nullable=False)
    communes = db.relationship('Commune', backref='wilaya', lazy=True)

class Commune(db.Model):
    __tablename__ = 'communes'
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(4), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_fr = db.Column(db.String(100), nullable=False)
    wilaya_id = db.Column(db.Integer, db.ForeignKey('wilayas.id'), nullable=False)

class Corps(db.Model):
    __tablename__ = 'corps'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    employees = db.relationship('Employee', backref='corps', lazy=True)

class Rank(db.Model):
    __tablename__ = 'ranks'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    level = db.Column(db.Integer, nullable=False)
    corps_id = db.Column(db.Integer, db.ForeignKey('corps.id'), nullable=False)
    employees = db.relationship('Employee', backref='current_rank', lazy=True)

class Position(db.Model):
    __tablename__ = 'positions'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    employees = db.relationship('Employee', backref='current_position', lazy=True)

class Directorate(db.Model):
    __tablename__ = 'directorates'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(10), unique=True, nullable=False)
    address = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    employees = db.relationship('Employee', backref='directorate', lazy=True)

class Service(db.Model):
    __tablename__ = 'services'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    directorate_id = db.Column(db.Integer, db.ForeignKey('directorates.id'), nullable=False)
    employees = db.relationship('Employee', backref='service', lazy=True)

class AssignmentLocation(db.Model):
    __tablename__ = 'assignment_locations'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    address = db.Column(db.String(200))
    wilaya_id = db.Column(db.Integer, db.ForeignKey('wilayas.id'))
    employees = db.relationship('Employee', backref='assignment_location', lazy=True)

class Employee(db.Model):
    __tablename__ = 'employees'
    id = db.Column(db.Integer, primary_key=True)
    registration_number = db.Column(db.String(6), unique=True, nullable=False)  # 6 أرقام فقط

    # 1- البيانات الأساسية
    last_name = db.Column(db.String(50), nullable=False)  # اللقب
    first_name = db.Column(db.String(50), nullable=False)  # الاسم
    nom_fr = db.Column(db.String(50))  # Nom بالفرنسية
    prenom_fr = db.Column(db.String(50))  # Prenom بالفرنسية
    gender = db.Column(db.String(10), nullable=False)  # الجنس
    birth_date = db.Column(db.Date, nullable=False)  # تاريخ الميلاد (19-65 سنة)
    birth_wilaya_id = db.Column(db.Integer, db.ForeignKey('wilayas.id'))  # ولاية الميلاد
    birth_commune_id = db.Column(db.Integer, db.ForeignKey('communes.id'))  # بلدية الميلاد
    marital_status = db.Column(db.String(20))  # الحالة العائلية
    children_count = db.Column(db.Integer, default=0)  # عدد الأبناء
    dependents_count = db.Column(db.Integer, default=0)  # عدد الأشخاص المتكفل بهم
    blood_type = db.Column(db.String(5))  # زمرة الدم
    photo = db.Column(db.String(100))  # صورة الموظف

    # 2- بيانات الاتصال
    phone1 = db.Column(db.String(20))  # رقم الهاتف 1
    phone2 = db.Column(db.String(20))  # رقم الهاتف 2
    email = db.Column(db.String(100))  # البريد الإلكتروني
    primary_address = db.Column(db.Text)  # العنوان الرئيسي
    secondary_address = db.Column(db.Text)  # العنوان الثانوي
    emergency_contact_name = db.Column(db.String(100))  # اسم الشخص المتصل به في حالة الضرورة
    emergency_contact_address = db.Column(db.Text)  # عنوان الشخص المتصل به في حالة الضرورة

    # 3- البيانات المهنية
    employee_status = db.Column(db.String(30), default='نشط')  # حالة الموظف
    corps_id = db.Column(db.Integer, db.ForeignKey('corps.id'), nullable=False)  # السلك
    current_rank_id = db.Column(db.Integer, db.ForeignKey('ranks.id'), nullable=False)  # الرتبة الحالية
    rank_promotion_date = db.Column(db.Date)  # تاريخ الترقية في الرتبة الحالية
    current_position_id = db.Column(db.Integer, db.ForeignKey('positions.id'), nullable=False)  # الوظيفة الحالية
    position_assignment_date = db.Column(db.Date)  # تاريخ التعيين في الوظيفة الحالية
    directorate_id = db.Column(db.Integer, db.ForeignKey('directorates.id'), nullable=False)  # المديرية
    service_id = db.Column(db.Integer, db.ForeignKey('services.id'))  # المصلحة
    assignment_location_id = db.Column(db.Integer, db.ForeignKey('assignment_locations.id'))  # مكان التعيين
    hiring_date = db.Column(db.Date, nullable=False)  # تاريخ التوظيف/الدخول في إدارة الجمارك
    hiring_rank_id = db.Column(db.Integer, db.ForeignKey('ranks.id'))  # رتبة التوظيف

    # الوثائق والأرقام الرسمية
    social_security_number = db.Column(db.String(15))  # رقم الضمان الاجتماعي (جزائري)
    postal_account_number = db.Column(db.String(20))  # رقم الحساب الجاري البريدي
    professional_card_number = db.Column(db.String(20))  # رقم بطاقة المهنية
    professional_card_issue_date = db.Column(db.Date)  # تاريخ صدور بطاقة المهنية
    national_id_number = db.Column(db.String(18))  # رقم بطاقة التعريف الوطنية
    national_id_issue_date = db.Column(db.Date)  # تاريخ صدور بطاقة التعريف
    national_id_issue_place_id = db.Column(db.Integer, db.ForeignKey('communes.id'))  # مكان صدور بطاقة التعريف
    driving_license_number = db.Column(db.String(20))  # رقم رخصة السياقة
    driving_license_category = db.Column(db.String(10))  # صنف رخصة السياقة
    driving_license_issue_date = db.Column(db.Date)  # تاريخ صدور رخصة السياقة
    driving_license_issue_place_id = db.Column(db.Integer, db.ForeignKey('communes.id'))  # مكان صدور رخصة السياقة
    mutual_card_number = db.Column(db.String(20))  # رقم بطاقة التعاضدية
    mutual_card_issue_date = db.Column(db.Date)  # تاريخ صدور بطاقة التعاضدية
    practiced_sport = db.Column(db.String(50))  # الرياضة الممارسة

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    birth_wilaya = db.relationship('Wilaya', foreign_keys=[birth_wilaya_id])
    birth_commune = db.relationship('Commune', foreign_keys=[birth_commune_id])
    national_id_issue_place = db.relationship('Commune', foreign_keys=[national_id_issue_place_id])
    driving_license_issue_place = db.relationship('Commune', foreign_keys=[driving_license_issue_place_id])
    spouses = db.relationship('Spouse', backref='employee', lazy=True)
    children = db.relationship('Child', backref='employee', lazy=True)
    corps = db.relationship('Corps', backref='employees')
    current_rank = db.relationship('Rank', foreign_keys=[current_rank_id], backref='current_employees')
    hiring_rank = db.relationship('Rank', foreign_keys=[hiring_rank_id], backref='hired_employees')
    current_position = db.relationship('Position', foreign_keys=[current_position_id], backref='employees')
    directorate = db.relationship('Directorate', backref='employees')
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_name_fr(self):
        if self.first_name_fr and self.last_name_fr:
            return f"{self.first_name_fr} {self.last_name_fr}"
        return self.full_name
    
    @property
    def age(self):
        today = date.today()
        return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))

class Spouse(db.Model):
    __tablename__ = 'spouses'
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    birth_date = db.Column(db.Date)
    profession = db.Column(db.String(100))
    national_id = db.Column(db.String(20))

class Child(db.Model):
    __tablename__ = 'children'
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    birth_date = db.Column(db.Date, nullable=False)
    gender = db.Column(db.String(10), nullable=False)
    education_level = db.Column(db.String(50))

# ===== دوال التحقق من صحة الأرقام الجزائرية =====

def validate_algerian_social_security(number):
    """التحقق من صحة رقم الضمان الاجتماعي الجزائري"""
    if not number or len(number) != 15:
        return False

    # التحقق من أن جميع الأرقام صحيحة
    if not number.isdigit():
        return False

    # استخراج المعلومات
    gender_code = number[0]  # 1 للذكر، 2 للأنثى
    birth_year = number[1:3]
    birth_month = number[3:5]
    birth_day = number[5:7]
    wilaya_code = number[7:9]

    # التحقق من صحة الجنس
    if gender_code not in ['1', '2']:
        return False

    # التحقق من صحة التاريخ
    try:
        month = int(birth_month)
        day = int(birth_day)
        if month < 1 or month > 12 or day < 1 or day > 31:
            return False
    except:
        return False

    # حساب المفتاح (الرقمين الأخيرين)
    base_number = number[:13]
    calculated_key = 97 - (int(base_number) % 97)
    actual_key = int(number[13:15])

    return calculated_key == actual_key

def validate_algerian_postal_account(number):
    """التحقق من صحة رقم الحساب الجاري البريدي الجزائري"""
    if not number or len(number) < 10 or len(number) > 20:
        return False

    if not number.isdigit():
        return False

    # حساب المفتاح للحساب الجاري البريدي
    base_number = number[:-2]
    key = number[-2:]

    try:
        calculated_key = 97 - (int(base_number) % 97)
        return f"{calculated_key:02d}" == key
    except:
        return False

def validate_algerian_national_id(number, birth_date=None, gender=None):
    """التحقق من صحة رقم بطاقة التعريف الوطنية الجزائرية"""
    if not number or len(number) != 18:
        return False

    if not number.isdigit():
        return False

    # استخراج المعلومات من الرقم
    birth_year = number[0:4]
    birth_month = number[4:6]
    birth_day = number[6:8]
    wilaya_code = number[8:10]
    gender_code = number[10]  # فردي للذكر، زوجي للأنثى

    # التحقق من صحة التاريخ
    try:
        year = int(birth_year)
        month = int(birth_month)
        day = int(birth_day)

        if year < 1900 or year > 2024:
            return False
        if month < 1 or month > 12:
            return False
        if day < 1 or day > 31:
            return False

        # مقارنة مع تاريخ الميلاد المدخل
        if birth_date:
            if (birth_date.year != year or
                birth_date.month != month or
                birth_date.day != day):
                return False

        # مقارنة مع الجنس المدخل
        if gender:
            is_male = int(gender_code) % 2 == 1
            if (gender == 'ذكر' and not is_male) or (gender == 'أنثى' and is_male):
                return False

    except:
        return False

    return True

# ===== النماذج (Forms) =====

class EmployeeForm(FlaskForm):
    # 1- البيانات الأساسية
    registration_number = StringField('رقم التسجيل', validators=[DataRequired(), Length(min=6, max=6)],
                                    render_kw={"placeholder": "123456", "pattern": "[0-9]{6}"})
    last_name = StringField('اللقب', validators=[DataRequired(), Length(min=2, max=50)])
    first_name = StringField('الاسم', validators=[DataRequired(), Length(min=2, max=50)])
    nom_fr = StringField('Nom (بالفرنسية)', validators=[Optional(), Length(max=50)])
    prenom_fr = StringField('Prénom (بالفرنسية)', validators=[Optional(), Length(max=50)])
    gender = SelectField('الجنس', choices=[('ذكر', 'ذكر'), ('أنثى', 'أنثى')], validators=[DataRequired()])
    birth_date = DateField('تاريخ الميلاد', validators=[DataRequired()])
    birth_wilaya_id = SelectField('ولاية الميلاد', coerce=int, validators=[Optional()])
    birth_commune_id = SelectField('بلدية الميلاد', coerce=int, validators=[Optional()])
    marital_status = SelectField('الحالة العائلية', choices=[
        ('', 'اختر الحالة العائلية'),
        ('أعزب', 'أعزب'), ('متزوج', 'متزوج'), ('مطلق', 'مطلق'), ('أرمل', 'أرمل')
    ], validators=[Optional()])
    children_count = IntegerField('عدد الأبناء', validators=[Optional(), NumberRange(min=0, max=20)], default=0)
    dependents_count = IntegerField('عدد الأشخاص المتكفل بهم', validators=[Optional(), NumberRange(min=0, max=20)], default=0)
    blood_type = SelectField('زمرة الدم', choices=[
        ('', 'اختر زمرة الدم'),
        ('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'),
        ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-')
    ], validators=[Optional()])
    photo = FileField('صورة الموظف')

    # 2- بيانات الاتصال
    phone1 = StringField('رقم الهاتف 1', validators=[Optional(), Length(max=20)])
    phone2 = StringField('رقم الهاتف 2', validators=[Optional(), Length(max=20)])
    email = StringField('البريد الإلكتروني', validators=[Optional(), Email(), Length(max=100)])
    primary_address = TextAreaField('العنوان الرئيسي', validators=[Optional()])
    secondary_address = TextAreaField('العنوان الثانوي', validators=[Optional()])
    emergency_contact_name = StringField('اسم الشخص المتصل به في حالة الضرورة', validators=[Optional(), Length(max=100)])
    emergency_contact_address = TextAreaField('عنوان الشخص المتصل به في حالة الضرورة', validators=[Optional()])

    # 3- البيانات المهنية
    employee_status = SelectField('حالة الموظف', choices=[
        ('نشط', 'نشط'), ('تحويل', 'تحويل'), ('موقف', 'موقف'), ('استيداع', 'استيداع'),
        ('منتدب', 'منتدب'), ('متوفي', 'متوفي'), ('مفصول', 'مفصول'), ('مستقيل', 'مستقيل')
    ], default='نشط', validators=[DataRequired()])

    corps_id = SelectField('السلك', choices=[
        ('', 'اختر السلك'),
        ('أسلاك خاصة', 'أسلاك خاصة'),
        ('أسلاك مشتركة', 'أسلاك مشتركة'),
        ('عمال مهنيين', 'عمال مهنيين')
    ], coerce=int, validators=[DataRequired()])

    current_rank_id = SelectField('الرتبة الحالية', coerce=int, validators=[DataRequired()])
    rank_promotion_date = DateField('تاريخ الترقية في الرتبة الحالية', validators=[Optional()])
    current_position_id = SelectField('الوظيفة الحالية', coerce=int, validators=[DataRequired()])
    position_assignment_date = DateField('تاريخ التعيين في الوظيفة الحالية', validators=[Optional()])
    directorate_id = SelectField('المديرية', coerce=int, validators=[DataRequired()])
    service_id = SelectField('المصلحة', coerce=int, validators=[Optional()])
    assignment_location_id = SelectField('مكان التعيين', coerce=int, validators=[Optional()])
    hiring_date = DateField('تاريخ التوظيف/الدخول في إدارة الجمارك', validators=[DataRequired()])
    hiring_rank_id = SelectField('رتبة التوظيف', coerce=int, validators=[Optional()])

    # الوثائق والأرقام الرسمية
    social_security_number = StringField('رقم الضمان الاجتماعي', validators=[Optional(), Length(min=15, max=15)],
                                       render_kw={"placeholder": "***************", "pattern": "[0-9]{15}"})
    postal_account_number = StringField('رقم الحساب الجاري البريدي', validators=[Optional()],
                                      render_kw={"placeholder": "***************67890"})
    professional_card_number = StringField('رقم بطاقة المهنية', validators=[Optional()])
    professional_card_issue_date = DateField('تاريخ صدور بطاقة المهنية', validators=[Optional()])
    national_id_number = StringField('رقم بطاقة التعريف الوطنية', validators=[Optional(), Length(min=18, max=18)],
                                   render_kw={"placeholder": "***************678", "pattern": "[0-9]{18}"})
    national_id_issue_date = DateField('تاريخ صدور بطاقة التعريف', validators=[Optional()])
    national_id_issue_place_id = SelectField('مكان صدور بطاقة التعريف', coerce=int, validators=[Optional()])
    driving_license_number = StringField('رقم رخصة السياقة', validators=[Optional()])
    driving_license_category = SelectField('صنف رخصة السياقة', choices=[
        ('', 'اختر الصنف'),
        ('A', 'A - دراجة نارية'), ('B', 'B - سيارة خفيفة'), ('C', 'C - شاحنة'),
        ('D', 'D - حافلة'), ('E', 'E - مقطورة')
    ], validators=[Optional()])
    driving_license_issue_date = DateField('تاريخ صدور رخصة السياقة', validators=[Optional()])
    driving_license_issue_place_id = SelectField('مكان صدور رخصة السياقة', coerce=int, validators=[Optional()])
    mutual_card_number = StringField('رقم بطاقة التعاضدية', validators=[Optional()])
    mutual_card_issue_date = DateField('تاريخ صدور بطاقة التعاضدية', validators=[Optional()])
    practiced_sport = StringField('الرياضة الممارسة', validators=[Optional(), Length(max=50)])

    def validate_registration_number(self, field):
        """التحقق من صحة رقم التسجيل"""
        if not field.data.isdigit():
            raise ValidationError('رقم التسجيل يجب أن يحتوي على أرقام فقط')
        if len(field.data) != 6:
            raise ValidationError('رقم التسجيل يجب أن يكون 6 أرقام بالضبط')

    def validate_birth_date(self, field):
        """التحقق من صحة تاريخ الميلاد (19-65 سنة)"""
        if field.data:
            today = date.today()
            age = today.year - field.data.year - ((today.month, today.day) < (field.data.month, field.data.day))
            if age < 19:
                raise ValidationError('عمر الموظف يجب أن يكون 19 سنة على الأقل')
            if age > 65:
                raise ValidationError('عمر الموظف يجب أن يكون أقل من 65 سنة')

    def validate_social_security_number(self, field):
        """التحقق من صحة رقم الضمان الاجتماعي"""
        if field.data and not validate_algerian_social_security(field.data):
            raise ValidationError('رقم الضمان الاجتماعي غير صحيح')

    def validate_postal_account_number(self, field):
        """التحقق من صحة رقم الحساب الجاري البريدي"""
        if field.data and not validate_algerian_postal_account(field.data):
            raise ValidationError('رقم الحساب الجاري البريدي غير صحيح')

    def validate_national_id_number(self, field):
        """التحقق من صحة رقم بطاقة التعريف الوطنية"""
        if field.data:
            birth_date = self.birth_date.data if hasattr(self, 'birth_date') else None
            gender = self.gender.data if hasattr(self, 'gender') else None
            if not validate_algerian_national_id(field.data, birth_date, gender):
                raise ValidationError('رقم بطاقة التعريف الوطنية غير صحيح أو لا يتطابق مع البيانات المدخلة')

    def validate_children_count(self, field):
        """التحقق من عدد الأبناء حسب الحالة العائلية"""
        if (self.marital_status.data == 'أعزب' and field.data and field.data > 0):
            raise ValidationError('لا يمكن أن يكون للأعزب أطفال')

class SpouseForm(FlaskForm):
    """نموذج إضافة الزوج/الزوجة"""
    first_name = StringField('الاسم', validators=[DataRequired(), Length(min=2, max=50)])
    last_name = StringField('اللقب', validators=[DataRequired(), Length(min=2, max=50)])
    birth_date = DateField('تاريخ الميلاد', validators=[Optional()])
    profession = StringField('المهنة', validators=[Optional(), Length(max=100)])
    workplace = StringField('مكان العمل', validators=[Optional(), Length(max=100)])

class ChildForm(FlaskForm):
    """نموذج إضافة الطفل"""
    first_name = StringField('الاسم', validators=[DataRequired(), Length(min=2, max=50)])
    last_name = StringField('اللقب', validators=[DataRequired(), Length(min=2, max=50)])
    birth_date = DateField('تاريخ الميلاد', validators=[DataRequired()])
    gender = SelectField('الجنس', choices=[('ذكر', 'ذكر'), ('أنثى', 'أنثى')], validators=[DataRequired()])
    education_level = StringField('المستوى التعليمي', validators=[Optional(), Length(max=50)])

# ===== الدوال المساعدة =====

def init_database():
    """تهيئة قاعدة البيانات بالبيانات الأساسية"""
    with app.app_context():
        db.create_all()
        
        # إضافة الولايات
        if not Wilaya.query.first():
            wilayas_data = [
                ('01', 'أدرار', 'Adrar'),
                ('02', 'الشلف', 'Chlef'),
                ('03', 'الأغواط', 'Laghouat'),
                ('04', 'أم البواقي', 'Oum El Bouaghi'),
                ('05', 'باتنة', 'Batna'),
                ('06', 'بجاية', 'Béjaïa'),
                ('07', 'بسكرة', 'Biskra'),
                ('08', 'بشار', 'Béchar'),
                ('09', 'البليدة', 'Blida'),
                ('10', 'البويرة', 'Bouira'),
                ('11', 'تمنراست', 'Tamanrasset'),
                ('12', 'تبسة', 'Tébessa'),
                ('13', 'تلمسان', 'Tlemcen'),
                ('14', 'تيارت', 'Tiaret'),
                ('15', 'تيزي وزو', 'Tizi Ouzou'),
                ('16', 'الجزائر', 'Alger'),
                ('17', 'الجلفة', 'Djelfa'),
                ('18', 'جيجل', 'Jijel'),
                ('19', 'سطيف', 'Sétif'),
                ('20', 'سعيدة', 'Saïda'),
                ('21', 'سكيكدة', 'Skikda'),
                ('22', 'سيدي بلعباس', 'Sidi Bel Abbès'),
                ('23', 'عنابة', 'Annaba'),
                ('24', 'قالمة', 'Guelma'),
                ('25', 'قسنطينة', 'Constantine'),
                ('26', 'المدية', 'Médéa'),
                ('27', 'مستغانم', 'Mostaganem'),
                ('28', 'المسيلة', 'M\'Sila'),
                ('29', 'معسكر', 'Mascara'),
                ('30', 'ورقلة', 'Ouargla'),
                ('31', 'وهران', 'Oran'),
                ('32', 'البيض', 'El Bayadh'),
                ('33', 'إليزي', 'Illizi'),
                ('34', 'برج بوعريريج', 'Bordj Bou Arréridj'),
                ('35', 'بومرداس', 'Boumerdès'),
                ('36', 'الطارف', 'El Tarf'),
                ('37', 'تندوف', 'Tindouf'),
                ('38', 'تيسمسيلت', 'Tissemsilt'),
                ('39', 'الوادي', 'El Oued'),
                ('40', 'خنشلة', 'Khenchela'),
                ('41', 'سوق أهراس', 'Souk Ahras'),
                ('42', 'تيبازة', 'Tipaza'),
                ('43', 'ميلة', 'Mila'),
                ('44', 'عين الدفلى', 'Aïn Defla'),
                ('45', 'النعامة', 'Naâma'),
                ('46', 'عين تموشنت', 'Aïn Témouchent'),
                ('47', 'غرداية', 'Ghardaïa'),
                ('48', 'غليزان', 'Relizane'),
                ('49', 'تيميمون', 'Timimoun'),
                ('50', 'برج باجي مختار', 'Bordj Badji Mokhtar'),
                ('51', 'أولاد جلال', 'Ouled Djellal'),
                ('52', 'بني عباس', 'Béni Abbès'),
                ('53', 'عين صالح', 'In Salah'),
                ('54', 'عين قزام', 'In Guezzam'),
                ('55', 'تقرت', 'Touggourt'),
                ('56', 'جانت', 'Djanet'),
                ('57', 'المغير', 'El M\'Ghair'),
                ('58', 'المنيعة', 'El Meniaa')
            ]
            
            for code, name_ar, name_fr in wilayas_data:
                wilaya = Wilaya(code=code, name_ar=name_ar, name_fr=name_fr)
                db.session.add(wilaya)
            
            db.session.commit()
            print("✅ تم إضافة الولايات")

        # إضافة الأسلاك
        if not Corps.query.first():
            corps_data = [
                ('أسلاك خاصة', 'الأسلاك الخاصة بالجمارك'),
                ('أسلاك مشتركة', 'الأسلاك المشتركة مع الإدارات الأخرى'),
                ('أسلاك تقنية', 'الأسلاك التقنية والمتخصصة'),
            ]

            for name, description in corps_data:
                corps = Corps(name=name, description=description)
                db.session.add(corps)

            db.session.commit()
            print("✅ تم إضافة الأسلاك")

        # إضافة الرتب
        if not Rank.query.first():
            ranks_data = [
                ('عون جمارك', 1, 1),
                ('عون جمارك رئيسي', 2, 1),
                ('مفتش للجمارك', 3, 1),
                ('مفتش رئيسي للجمارك', 4, 1),
                ('مفتش مركزي للجمارك', 5, 1),
                ('مدير دراسات', 6, 1),
                ('مدير دراسات رئيسي', 7, 1),
                ('مدير دراسات مركزي', 8, 1),
            ]

            for name, level, corps_id in ranks_data:
                rank = Rank(name=name, level=level, corps_id=corps_id)
                db.session.add(rank)

            db.session.commit()
            print("✅ تم إضافة الرتب")

        # إضافة الوظائف
        if not Position.query.first():
            positions_data = [
                ('مكلف بالموارد البشرية', 'إدارة شؤون الموظفين'),
                ('مكلف بالمالية والمحاسبة', 'إدارة الشؤون المالية'),
                ('مكلف بالتفتيش', 'أعمال التفتيش والمراقبة'),
                ('مكلف بالتكوين', 'التكوين والتدريب'),
                ('مكلف بالإحصائيات', 'جمع وتحليل الإحصائيات'),
                ('رئيس مكتب', 'إدارة مكتب'),
                ('رئيس مصلحة', 'إدارة مصلحة'),
                ('رئيس قسم', 'إدارة قسم'),
                ('مدير', 'إدارة مديرية'),
                ('كاتب', 'أعمال كتابية'),
                ('محاسب', 'أعمال محاسبية'),
                ('مفتش ميداني', 'التفتيش الميداني'),
            ]

            for name, description in positions_data:
                position = Position(name=name, description=description)
                db.session.add(position)

            db.session.commit()
            print("✅ تم إضافة الوظائف")

        # إضافة المديريات
        if not Directorate.query.first():
            directorates_data = [
                ('المديرية العامة للجمارك', 'DGD', 'الجزائر العاصمة', '021-XX-XX-XX'),
                ('المديرية الجهوية للجمارك - الجزائر', 'DRD-ALG', 'الجزائر', '021-XX-XX-XX'),
                ('المديرية الجهوية للجمارك - وهران', 'DRD-ORA', 'وهران', '041-XX-XX-XX'),
                ('المديرية الجهوية للجمارك - قسنطينة', 'DRD-CST', 'قسنطينة', '031-XX-XX-XX'),
                ('المديرية الجهوية للجمارك - ورقلة', 'DRD-OUA', 'ورقلة', '029-XX-XX-XX'),
                ('مديرية الموارد البشرية', 'DRH', 'الجزائر العاصمة', '021-XX-XX-XX'),
                ('مديرية المالية والمحاسبة', 'DFC', 'الجزائر العاصمة', '021-XX-XX-XX'),
                ('مديرية التكوين', 'DF', 'الجزائر العاصمة', '021-XX-XX-XX'),
            ]

            for name, code, address, phone in directorates_data:
                directorate = Directorate(name=name, code=code, address=address, phone=phone)
                db.session.add(directorate)

            db.session.commit()
            print("✅ تم إضافة المديريات")

        # إضافة المصالح
        if not Service.query.first():
            services_data = [
                ('مصلحة تسيير المستخدمين', 1),
                ('مصلحة التكوين والتطوير', 1),
                ('مصلحة الأجور والمنح', 1),
                ('مصلحة المحاسبة العامة', 2),
                ('مصلحة الميزانية', 2),
                ('مصلحة المراقبة المالية', 2),
                ('مصلحة التفتيش الداخلي', 3),
                ('مصلحة التفتيش الخارجي', 3),
                ('مصلحة المراقبة', 3),
            ]

            for name, directorate_id in services_data:
                service = Service(name=name, directorate_id=directorate_id)
                db.session.add(service)

            db.session.commit()
            print("✅ تم إضافة المصالح")

        # إضافة أماكن التعيين
        if not AssignmentLocation.query.first():
            locations_data = [
                ('مطار هواري بومدين الدولي', 'الجزائر العاصمة', 1),
                ('ميناء الجزائر', 'الجزائر العاصمة', 1),
                ('مطار وهران أحمد بن بلة', 'وهران', 2),
                ('ميناء وهران', 'وهران', 2),
                ('مطار قسنطينة محمد بوضياف', 'قسنطينة', 3),
                ('المعبر الحدودي - الحجار', 'الطارف', 4),
                ('المعبر الحدودي - تبسة', 'تبسة', 5),
                ('مركز جمركي - غرداية', 'غرداية', 6),
                ('مركز جمركي - تمنراست', 'تمنراست', 7),
                ('مركز جمركي - إليزي', 'إليزي', 8),
            ]

            for name, address, wilaya_id in locations_data:
                location = AssignmentLocation(name=name, address=address, wilaya_id=wilaya_id)
                db.session.add(location)

            db.session.commit()
            print("✅ تم إضافة أماكن التعيين")

        # إضافة موظفين تجريبيين
        if not Employee.query.first():
            sample_employees = [
                {
                    'registration_number': '123456',
                    'last_name': 'بن محمد',
                    'first_name': 'أحمد',
                    'nom_fr': 'Ben Mohamed',
                    'prenom_fr': 'Ahmed',
                    'gender': 'ذكر',
                    'birth_date': datetime(1985, 5, 15).date(),
                    'birth_wilaya_id': 1,
                    'birth_commune_id': 1,
                    'marital_status': 'متزوج',
                    'children_count': 2,
                    'dependents_count': 2,
                    'blood_type': 'A+',
                    'phone1': '0555123456',
                    'email': '<EMAIL>',
                    'primary_address': 'حي السلام، الجزائر العاصمة',
                    'emergency_contact_name': 'فاطمة بن محمد',
                    'emergency_contact_address': 'نفس العنوان',
                    'employee_status': 'نشط',
                    'corps_id': 1,
                    'current_rank_id': 3,
                    'rank_promotion_date': datetime(2020, 1, 1).date(),
                    'current_position_id': 1,
                    'position_assignment_date': datetime(2018, 6, 1).date(),
                    'directorate_id': 1,
                    'service_id': 1,
                    'assignment_location_id': 1,
                    'hiring_date': datetime(2010, 9, 1).date(),
                    'hiring_rank_id': 1,
                    'social_security_number': '***************',
                    'postal_account_number': '***************67890',
                    'professional_card_number': 'PC123456',
                    'professional_card_issue_date': datetime(2010, 10, 1).date(),
                    'national_id_number': '198505151234567890',
                    'national_id_issue_date': datetime(2005, 1, 1).date(),
                    'national_id_issue_place_id': 1,
                    'driving_license_number': 'DL123456',
                    'driving_license_category': 'B',
                    'driving_license_issue_date': datetime(2008, 3, 15).date(),
                    'driving_license_issue_place_id': 1,
                    'mutual_card_number': 'MC123456',
                    'mutual_card_issue_date': datetime(2010, 9, 15).date(),
                    'practiced_sport': 'كرة القدم'
                },
                {
                    'registration_number': '789012',
                    'last_name': 'علي',
                    'first_name': 'فاطمة',
                    'nom_fr': 'Ali',
                    'prenom_fr': 'Fatima',
                    'gender': 'أنثى',
                    'birth_date': datetime(1990, 3, 20).date(),
                    'birth_wilaya_id': 2,
                    'birth_commune_id': 2,
                    'marital_status': 'أعزب',
                    'children_count': 0,
                    'dependents_count': 1,
                    'blood_type': 'O+',
                    'phone1': '**********',
                    'email': '<EMAIL>',
                    'primary_address': 'حي الأندلس، وهران',
                    'emergency_contact_name': 'محمد علي',
                    'emergency_contact_address': 'حي النصر، وهران',
                    'employee_status': 'نشط',
                    'corps_id': 1,
                    'current_rank_id': 2,
                    'rank_promotion_date': datetime(2019, 6, 1).date(),
                    'current_position_id': 2,
                    'position_assignment_date': datetime(2017, 3, 1).date(),
                    'directorate_id': 2,
                    'service_id': 2,
                    'assignment_location_id': 3,
                    'hiring_date': datetime(2015, 2, 15).date(),
                    'hiring_rank_id': 1,
                    'social_security_number': '***************',
                    'postal_account_number': '98765432109876543210',
                    'professional_card_number': 'PC789012',
                    'professional_card_issue_date': datetime(2015, 3, 1).date(),
                    'national_id_number': '199003201234567891',
                    'national_id_issue_date': datetime(2010, 1, 1).date(),
                    'national_id_issue_place_id': 2,
                    'driving_license_number': 'DL789012',
                    'driving_license_category': 'B',
                    'driving_license_issue_date': datetime(2012, 7, 20).date(),
                    'driving_license_issue_place_id': 2,
                    'mutual_card_number': 'MC789012',
                    'mutual_card_issue_date': datetime(2015, 2, 28).date(),
                    'practiced_sport': 'السباحة'
                },
                {
                    'registration_number': '345678',
                    'last_name': 'حسن',
                    'first_name': 'محمد',
                    'nom_fr': 'Hassan',
                    'prenom_fr': 'Mohamed',
                    'gender': 'ذكر',
                    'birth_date': datetime(1982, 12, 10).date(),
                    'birth_wilaya_id': 3,
                    'birth_commune_id': 3,
                    'marital_status': 'متزوج',
                    'children_count': 3,
                    'dependents_count': 5,
                    'blood_type': 'B+',
                    'phone1': '**********',
                    'phone2': '**********',
                    'email': '<EMAIL>',
                    'primary_address': 'حي بودراع صالح، قسنطينة',
                    'secondary_address': 'حي الجامعة، قسنطينة',
                    'emergency_contact_name': 'خديجة حسن',
                    'emergency_contact_address': 'حي بودراع صالح، قسنطينة',
                    'employee_status': 'في إجازة',
                    'corps_id': 1,
                    'current_rank_id': 4,
                    'rank_promotion_date': datetime(2018, 1, 1).date(),
                    'current_position_id': 6,
                    'position_assignment_date': datetime(2015, 9, 1).date(),
                    'directorate_id': 3,
                    'service_id': 7,
                    'assignment_location_id': 5,
                    'hiring_date': datetime(2008, 6, 1).date(),
                    'hiring_rank_id': 1,
                    'social_security_number': '***************',
                    'postal_account_number': '45678901234567890123',
                    'professional_card_number': 'PC345678',
                    'professional_card_issue_date': datetime(2008, 7, 1).date(),
                    'national_id_number': '198212101234567892',
                    'national_id_issue_date': datetime(2003, 1, 1).date(),
                    'national_id_issue_place_id': 3,
                    'driving_license_number': 'DL345678',
                    'driving_license_category': 'B',
                    'driving_license_issue_date': datetime(2005, 11, 10).date(),
                    'driving_license_issue_place_id': 3,
                    'mutual_card_number': 'MC345678',
                    'mutual_card_issue_date': datetime(2008, 6, 15).date(),
                    'practiced_sport': 'كرة السلة'
                }
            ]

            for emp_data in sample_employees:
                employee = Employee(**emp_data)
                db.session.add(employee)

            db.session.commit()
            print("✅ تم إضافة الموظفين التجريبيين")

# ===== الروتات (Routes) =====

@app.route('/')
def index():
    """الصفحة الرئيسية - لوحة التحكم"""
    total_employees = Employee.query.count()
    active_employees = Employee.query.filter_by(status='نشط').count()
    total_directorates = Directorate.query.count()
    total_services = Service.query.count()

    # الموظفين الجدد (آخر 30 يوم)
    from datetime import timedelta
    thirty_days_ago = datetime.now() - timedelta(days=30)
    new_employees = Employee.query.filter(Employee.created_at >= thirty_days_ago).count()

    # إحصائيات حسب الجنس
    male_count = Employee.query.filter_by(gender='ذكر').count()
    female_count = Employee.query.filter_by(gender='أنثى').count()

    # إحصائيات حسب الحالة
    status_stats = db.session.query(Employee.status, db.func.count(Employee.id)).group_by(Employee.status).all()

    return render_template('complete/dashboard.html',
                         total_employees=total_employees,
                         active_employees=active_employees,
                         total_directorates=total_directorates,
                         total_services=total_services,
                         new_employees=new_employees,
                         male_count=male_count,
                         female_count=female_count,
                         status_stats=status_stats)

@app.route('/employees')
def employees_list():
    """قائمة الموظفين مع البحث والتصفية"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status_filter = request.args.get('status', '')
    directorate_filter = request.args.get('directorate', '', type=int)
    gender_filter = request.args.get('gender', '')

    query = Employee.query

    # تطبيق البحث
    if search:
        query = query.filter(
            db.or_(
                Employee.first_name.contains(search),
                Employee.last_name.contains(search),
                Employee.registration_number.contains(search),
                Employee.email.contains(search)
            )
        )

    # تطبيق الفلاتر
    if status_filter:
        query = query.filter_by(status=status_filter)

    if directorate_filter:
        query = query.filter_by(directorate_id=directorate_filter)

    if gender_filter:
        query = query.filter_by(gender=gender_filter)

    # ترتيب النتائج
    query = query.order_by(Employee.created_at.desc())

    # تقسيم الصفحات
    employees = query.paginate(
        page=page, per_page=20, error_out=False
    )

    # جلب البيانات للفلاتر
    directorates = Directorate.query.all()

    return render_template('complete/employees_list.html',
                         employees=employees,
                         search=search,
                         status_filter=status_filter,
                         directorate_filter=directorate_filter,
                         gender_filter=gender_filter,
                         directorates=directorates)

@app.route('/employee/add', methods=['GET', 'POST'])
def add_employee():
    """إضافة موظف جديد"""
    form = EmployeeForm()

    # تحديث خيارات النماذج
    form.birth_wilaya_id.choices = [(0, 'اختر الولاية')] + [(w.id, w.name_ar) for w in Wilaya.query.all()]
    form.birth_commune_id.choices = [(0, 'اختر البلدية')]
    form.corps_id.choices = [(c.id, c.name) for c in Corps.query.all()]
    form.current_rank_id.choices = [(r.id, r.name) for r in Rank.query.all()]
    form.hiring_rank_id.choices = [(0, 'اختر الرتبة')] + [(r.id, r.name) for r in Rank.query.all()]
    form.current_position_id.choices = [(p.id, p.name) for p in Position.query.all()]
    form.directorate_id.choices = [(d.id, d.name) for d in Directorate.query.all()]
    form.service_id.choices = [(0, 'اختر المصلحة')] + [(s.id, s.name) for s in Service.query.all()]
    form.assignment_location_id.choices = [(0, 'اختر مكان التعيين')] + [(l.id, l.name) for l in AssignmentLocation.query.all()]
    form.national_id_issue_place_id.choices = [(0, 'اختر مكان الإصدار')] + [(c.id, c.name_ar) for c in Commune.query.all()]
    form.driving_license_issue_place_id.choices = [(0, 'اختر مكان الإصدار')] + [(c.id, c.name_ar) for c in Commune.query.all()]

    if form.validate_on_submit():
        # التحقق من عدم تكرار رقم التسجيل
        existing_employee = Employee.query.filter_by(registration_number=form.registration_number.data).first()
        if existing_employee:
            flash('رقم التسجيل موجود مسبقاً', 'error')
            return render_template('complete/employee_form_detailed.html', form=form, title='إضافة موظف جديد')

        # معالجة رفع الصورة
        photo_filename = None
        if form.photo.data:
            photo_file = form.photo.data
            if photo_file.filename:
                photo_filename = secure_filename(f"{form.registration_number.data}_{photo_file.filename}")
                photo_path = os.path.join('static/photos', photo_filename)
                os.makedirs('static/photos', exist_ok=True)
                photo_file.save(photo_path)

        # إنشاء موظف جديد
        employee = Employee(
            registration_number=form.registration_number.data,
            last_name=form.last_name.data,
            first_name=form.first_name.data,
            nom_fr=form.nom_fr.data,
            prenom_fr=form.prenom_fr.data,
            gender=form.gender.data,
            birth_date=form.birth_date.data,
            birth_wilaya_id=form.birth_wilaya_id.data if form.birth_wilaya_id.data else None,
            birth_commune_id=form.birth_commune_id.data if form.birth_commune_id.data else None,
            marital_status=form.marital_status.data,
            children_count=form.children_count.data,
            dependents_count=form.dependents_count.data,
            blood_type=form.blood_type.data,
            phone1=form.phone1.data,
            phone2=form.phone2.data,
            email=form.email.data,
            primary_address=form.primary_address.data,
            secondary_address=form.secondary_address.data,
            emergency_contact_name=form.emergency_contact_name.data,
            emergency_contact_address=form.emergency_contact_address.data,
            employee_status=form.employee_status.data,
            corps_id=form.corps_id.data,
            current_rank_id=form.current_rank_id.data,
            rank_promotion_date=form.rank_promotion_date.data,
            current_position_id=form.current_position_id.data,
            position_assignment_date=form.position_assignment_date.data,
            directorate_id=form.directorate_id.data,
            service_id=form.service_id.data if form.service_id.data else None,
            assignment_location_id=form.assignment_location_id.data if form.assignment_location_id.data else None,
            hiring_date=form.hiring_date.data,
            hiring_rank_id=form.hiring_rank_id.data if form.hiring_rank_id.data else None,
            social_security_number=form.social_security_number.data,
            postal_account_number=form.postal_account_number.data,
            professional_card_number=form.professional_card_number.data,
            professional_card_issue_date=form.professional_card_issue_date.data,
            national_id_number=form.national_id_number.data,
            national_id_issue_date=form.national_id_issue_date.data,
            national_id_issue_place_id=form.national_id_issue_place_id.data if form.national_id_issue_place_id.data else None,
            driving_license_number=form.driving_license_number.data,
            driving_license_category=form.driving_license_category.data,
            driving_license_issue_date=form.driving_license_issue_date.data,
            driving_license_issue_place_id=form.driving_license_issue_place_id.data if form.driving_license_issue_place_id.data else None,
            mutual_card_number=form.mutual_card_number.data,
            mutual_card_issue_date=form.mutual_card_issue_date.data,
            practiced_sport=form.practiced_sport.data,
            photo=photo_filename
        )

        db.session.add(employee)
        db.session.commit()

        flash('تم إضافة الموظف بنجاح', 'success')
        return redirect(url_for('employee_detail', id=employee.id))

    return render_template('complete/employee_form_detailed.html', form=form, title='إضافة موظف جديد')

@app.route('/employee/<int:id>')
def employee_detail(id):
    """عرض تفاصيل الموظف"""
    employee = Employee.query.get_or_404(id)
    return render_template('complete/employee_detail.html', employee=employee)

@app.route('/employee/<int:id>/edit', methods=['GET', 'POST'])
def edit_employee(id):
    """تعديل بيانات الموظف"""
    employee = Employee.query.get_or_404(id)
    form = EmployeeForm(obj=employee)

    # تحديث خيارات النماذج
    form.birth_wilaya_id.choices = [(0, 'اختر الولاية')] + [(w.id, w.name_ar) for w in Wilaya.query.all()]
    form.birth_commune_id.choices = [(0, 'اختر البلدية')]
    form.corps_id.choices = [(c.id, c.name) for c in Corps.query.all()]
    form.rank_id.choices = [(r.id, r.name) for r in Rank.query.all()]
    form.position_id.choices = [(p.id, p.name) for p in Position.query.all()]
    form.directorate_id.choices = [(d.id, d.name) for d in Directorate.query.all()]
    form.service_id.choices = [(0, 'اختر المصلحة')] + [(s.id, s.name) for s in Service.query.all()]

    if form.validate_on_submit():
        # التحقق من عدم تكرار رقم التسجيل
        existing_employee = Employee.query.filter(
            Employee.registration_number == form.registration_number.data,
            Employee.id != id
        ).first()

        if existing_employee:
            flash('رقم التسجيل موجود مسبقاً', 'error')
            return render_template('complete/employee_form_detailed.html', form=form, title='تعديل بيانات الموظف', employee=employee)

        # تحديث البيانات
        form.populate_obj(employee)
        employee.updated_at = datetime.utcnow()

        db.session.commit()
        flash('تم تحديث بيانات الموظف بنجاح', 'success')
        return redirect(url_for('employee_detail', id=employee.id))

    return render_template('complete/employee_form_detailed.html', form=form, title='تعديل بيانات الموظف', employee=employee)

@app.route('/settings')
def settings():
    """صفحة الإعدادات الرئيسية"""
    return render_template('complete/settings.html')

@app.route('/settings/administrative_divisions')
def administrative_divisions():
    """إدارة التقسيم الإداري"""
    wilayas = Wilaya.query.all()
    return render_template('complete/administrative_divisions.html', wilayas=wilayas)

@app.route('/settings/ranks')
def ranks_settings():
    """إدارة الرتب"""
    ranks = Rank.query.all()
    corps = Corps.query.all()
    return render_template('complete/ranks.html', ranks=ranks, corps=corps)

@app.route('/settings/positions')
def positions_settings():
    """إدارة الوظائف"""
    positions = Position.query.all()
    return render_template('complete/positions.html', positions=positions)

@app.route('/api/communes/<int:wilaya_id>')
def get_communes(wilaya_id):
    """API لجلب البلديات حسب الولاية"""
    communes = Commune.query.filter_by(wilaya_id=wilaya_id).all()
    return jsonify([{'id': c.id, 'name': c.name_ar} for c in communes])

@app.route('/api/services/<int:directorate_id>')
def get_services(directorate_id):
    """API لجلب المصالح حسب المديرية"""
    services = Service.query.filter_by(directorate_id=directorate_id).all()
    return jsonify([{'id': s.id, 'name': s.name} for s in services])

@app.route('/employee/<int:id>/delete', methods=['POST'])
def delete_employee(id):
    """حذف موظف"""
    employee = Employee.query.get_or_404(id)

    try:
        # حذف السجلات المرتبطة
        Spouse.query.filter_by(employee_id=id).delete()
        Child.query.filter_by(employee_id=id).delete()

        # حذف الموظف
        db.session.delete(employee)
        db.session.commit()

        flash('تم حذف الموظف بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في حذف الموظف: {str(e)}', 'error')

    return redirect(url_for('employees_list'))

@app.route('/reports')
def reports():
    """صفحة التقارير"""
    return render_template('complete/reports.html')

# Context processors
@app.context_processor
def utility_processor():
    """إضافة دوال مساعدة للقوالب"""
    def format_date(date_obj):
        if date_obj:
            return date_obj.strftime('%d/%m/%Y')
        return ''

    def format_datetime(datetime_obj):
        if datetime_obj:
            return datetime_obj.strftime('%d/%m/%Y %H:%M')
        return ''

    def current_year():
        return datetime.now().year

    def moment():
        return datetime.now()

    return dict(
        format_date=format_date,
        format_datetime=format_datetime,
        current_year=current_year,
        moment=moment,
        datetime=datetime
    )

if __name__ == '__main__':
    init_database()
    print("\n" + "=" * 80)
    print("🎯 بدء تشغيل النظام المتكامل...")
    print("📍 العنوان: http://localhost:5000")
    print("🔧 للإيقاف: اضغط Ctrl+C")
    print("=" * 80)
    print("✅ النظام المتكامل جاهز!")
    print()

    try:
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
    finally:
        print("👋 شكراً لاستخدام نظام إدارة الجمارك الجزائرية")

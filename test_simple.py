#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للتطبيق
"""

try:
    print("بدء اختبار التطبيق...")
    
    # اختبار استيراد Flask
    print("1. اختبار استيراد Flask...")
    from flask import Flask
    print("   ✓ تم استيراد Flask بنجاح")
    
    # اختبار استيراد SQLAlchemy
    print("2. اختبار استيراد SQLAlchemy...")
    from flask_sqlalchemy import SQLAlchemy
    print("   ✓ تم استيراد SQLAlchemy بنجاح")
    
    # اختبار استيراد WTForms
    print("3. اختبار استيراد WTForms...")
    from flask_wtf import FlaskForm
    from wtforms import StringField
    print("   ✓ تم استيراد WTForms بنجاح")
    
    # اختبار استيراد Pillow
    print("4. اختبار استيراد Pillow...")
    from PIL import Image
    print("   ✓ تم استيراد Pillow بنجاح")
    
    # اختبار إنشاء تطبيق Flask بسيط
    print("5. اختبار إنشاء تطبيق Flask...")
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test-key'
    print("   ✓ تم إنشاء تطبيق Flask بنجاح")
    
    # اختبار استيراد ملف التكوين
    print("6. اختبار استيراد ملف التكوين...")
    from config import get_config, CUSTOMS_CONFIG
    config = get_config()
    print(f"   ✓ تم استيراد التكوين بنجاح: {config.__name__}")
    
    # اختبار استيراد النماذج
    print("7. اختبار استيراد النماذج...")
    app.config.from_object(config)
    db = SQLAlchemy(app)
    
    with app.app_context():
        # استيراد النماذج
        from models import Wilaya, Employee, Corps
        print("   ✓ تم استيراد النماذج بنجاح")
        
        # إنشاء الجداول
        print("8. اختبار إنشاء الجداول...")
        db.create_all()
        print("   ✓ تم إنشاء الجداول بنجاح")
        
        # اختبار إضافة بيانات
        print("9. اختبار إضافة بيانات...")
        wilaya_count = Wilaya.query.count()
        print(f"   ✓ عدد الولايات في قاعدة البيانات: {wilaya_count}")
        
        if wilaya_count == 0:
            print("   - إضافة ولاية للاختبار...")
            test_wilaya = Wilaya(code='16', name_ar='الجزائر', name_fr='Alger')
            db.session.add(test_wilaya)
            db.session.commit()
            print("   ✓ تم إضافة ولاية الاختبار")
    
    # اختبار استيراد النماذج
    print("10. اختبار استيراد نماذج الإدخال...")
    from forms import EmployeeForm
    print("   ✓ تم استيراد نماذج الإدخال بنجاح")
    
    print("\n" + "="*50)
    print("✅ جميع الاختبارات نجحت!")
    print("التطبيق جاهز للتشغيل")
    print("="*50)
    
    # معلومات إضافية
    print(f"\nمعلومات التكوين:")
    print(f"- قاعدة البيانات: {app.config['SQLALCHEMY_DATABASE_URI']}")
    print(f"- مجلد الرفع: {app.config['UPLOAD_FOLDER']}")
    print(f"- الحد الأقصى لحجم الملف: {app.config['MAX_CONTENT_LENGTH']} بايت")
    
    print(f"\nإعدادات الجمارك:")
    print(f"- الحد الأدنى للعمر: {CUSTOMS_CONFIG['MIN_AGE']} سنة")
    print(f"- الحد الأقصى للعمر: {CUSTOMS_CONFIG['MAX_AGE']} سنة")
    print(f"- طول رقم التسجيل: {CUSTOMS_CONFIG['REGISTRATION_NUMBER_LENGTH']} أرقام")

except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("تأكد من تثبيت جميع المتطلبات باستخدام: pip install -r requirements.txt")

except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()

print("\nانتهى الاختبار.")

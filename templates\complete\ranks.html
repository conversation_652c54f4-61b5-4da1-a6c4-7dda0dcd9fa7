{% extends "professional_base.html" %}

{% block title %}إدارة الرتب والأسلاك - نظام إدارة الجمارك الجزائرية{% endblock %}

{% block page_title %}إدارة الرتب والأسلاك{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item"><a href="{{ url_for('settings') }}">الإعدادات</a></li>
<li class="breadcrumb-item active">الرتب والأسلاك</li>
{% endblock %}

{% block content %}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-medal mr-2"></i>
            إدارة الرتب والأسلاك
        </h1>
        <p class="text-muted">إدارة الهيكل الوظيفي للجمارك الجزائرية</p>
    </div>
    <div class="col-md-4 text-left">
        <div class="btn-group">
            <button class="btn btn-primary" data-toggle="modal" data-target="#addCorpsModal">
                <i class="fas fa-plus mr-1"></i>
                إضافة سلك
            </button>
            <button class="btn btn-success" data-toggle="modal" data-target="#addRankModal">
                <i class="fas fa-plus mr-1"></i>
                إضافة رتبة
            </button>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card primary">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">إجمالي الأسلاك</div>
                    <div class="stats-number">{{ corps|length }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-layer-group stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card success">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">إجمالي الرتب</div>
                    <div class="stats-number">{{ ranks|length }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-medal stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card warning">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">أعلى مستوى</div>
                    <div class="stats-number">{{ ranks|map(attribute='level')|max if ranks else 0 }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-arrow-up stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Corps and Ranks -->
<div class="row">
    <!-- Corps List -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-layer-group mr-2"></i>
                    الأسلاك
                </h5>
            </div>
            <div class="card-body">
                {% for corps_item in corps %}
                <div class="card mb-3 corps-card" data-corps-id="{{ corps_item.id }}">
                    <div class="card-body p-3">
                        <h6 class="card-title mb-2">{{ corps_item.name }}</h6>
                        <p class="card-text text-muted small mb-2">{{ corps_item.description }}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge badge-info">{{ corps_item.ranks|length }} رتبة</span>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="viewCorpsRanks({{ corps_item.id }}, '{{ corps_item.name }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" onclick="editCorps({{ corps_item.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Ranks List -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-medal mr-2"></i>
                    الرتب
                    <span id="ranksTitle"></span>
                </h5>
            </div>
            <div class="card-body">
                <div id="ranksContent">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الرتبة</th>
                                    <th>المستوى</th>
                                    <th>السلك</th>
                                    <th>عدد الموظفين</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rank in ranks %}
                                <tr class="rank-row" data-corps-id="{{ rank.corps_id }}">
                                    <td>
                                        <strong>{{ rank.name }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">المستوى {{ rank.level }}</span>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ rank.corps.name }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ rank.employees|length }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-warning" onclick="editRank({{ rank.id }})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteRank({{ rank.id }}, '{{ rank.name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Corps Modal -->
<div class="modal fade" id="addCorpsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة سلك جديد</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addCorpsForm">
                    <div class="form-group">
                        <label for="corpsName">اسم السلك</label>
                        <input type="text" class="form-control" id="corpsName" required>
                    </div>
                    <div class="form-group">
                        <label for="corpsDescription">الوصف</label>
                        <textarea class="form-control" id="corpsDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveCorps()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Rank Modal -->
<div class="modal fade" id="addRankModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة رتبة جديدة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addRankForm">
                    <div class="form-group">
                        <label for="rankName">اسم الرتبة</label>
                        <input type="text" class="form-control" id="rankName" required>
                    </div>
                    <div class="form-group">
                        <label for="rankLevel">المستوى</label>
                        <input type="number" class="form-control" id="rankLevel" min="1" max="20" required>
                    </div>
                    <div class="form-group">
                        <label for="rankCorps">السلك</label>
                        <select class="form-control" id="rankCorps" required>
                            <option value="">اختر السلك</option>
                            {% for corps_item in corps %}
                            <option value="{{ corps_item.id }}">{{ corps_item.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="saveRank()">حفظ</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تفعيل تأثيرات التحويم
    $('.corps-card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );
    
    // النقر على السلك لعرض رتبه
    $('.corps-card').click(function() {
        const corpsId = $(this).data('corps-id');
        const corpsName = $(this).find('.card-title').text();
        viewCorpsRanks(corpsId, corpsName);
    });
});

function viewCorpsRanks(corpsId, corpsName) {
    // تمييز السلك المحدد
    $('.corps-card').removeClass('border-primary');
    $(`.corps-card[data-corps-id="${corpsId}"]`).addClass('border-primary');
    
    // تحديث عنوان الرتب
    $('#ranksTitle').text(`- ${corpsName}`);
    
    // إخفاء جميع الرتب وإظهار رتب السلك المحدد فقط
    $('.rank-row').hide();
    $(`.rank-row[data-corps-id="${corpsId}"]`).show();
    
    // إذا لم توجد رتب للسلك
    if ($(`.rank-row[data-corps-id="${corpsId}"]`).length === 0) {
        $('#ranksContent').html(`
            <div class="text-center py-4">
                <i class="fas fa-medal fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد رتب لهذا السلك</h5>
                <button class="btn btn-success" data-toggle="modal" data-target="#addRankModal" onclick="setCorpsForNewRank(${corpsId})">
                    <i class="fas fa-plus mr-1"></i>
                    إضافة رتبة جديدة
                </button>
            </div>
        `);
    }
}

function setCorpsForNewRank(corpsId) {
    $('#rankCorps').val(corpsId);
}

function editCorps(corpsId) {
    alert('تعديل السلك رقم: ' + corpsId);
}

function editRank(rankId) {
    alert('تعديل الرتبة رقم: ' + rankId);
}

function deleteRank(rankId, rankName) {
    if (confirm(`هل أنت متأكد من حذف الرتبة: ${rankName}؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        alert('سيتم حذف الرتبة...');
    }
}

function saveCorps() {
    const name = $('#corpsName').val();
    const description = $('#corpsDescription').val();
    
    if (!name) {
        alert('يرجى إدخال اسم السلك');
        return;
    }
    
    alert('تم حفظ السلك بنجاح');
    $('#addCorpsModal').modal('hide');
    location.reload();
}

function saveRank() {
    const name = $('#rankName').val();
    const level = $('#rankLevel').val();
    const corpsId = $('#rankCorps').val();
    
    if (!name || !level || !corpsId) {
        alert('يرجى ملء جميع الحقول');
        return;
    }
    
    alert('تم حفظ الرتبة بنجاح');
    $('#addRankModal').modal('hide');
    location.reload();
}

// إظهار جميع الرتب عند تحميل الصفحة
function showAllRanks() {
    $('.corps-card').removeClass('border-primary');
    $('#ranksTitle').text('- جميع الأسلاك');
    $('.rank-row').show();
}

// زر لإظهار جميع الرتب
$(document).ready(function() {
    $('#ranksContent').prepend(`
        <div class="mb-3">
            <button class="btn btn-outline-secondary btn-sm" onclick="showAllRanks()">
                <i class="fas fa-list mr-1"></i>
                عرض جميع الرتب
            </button>
        </div>
    `);
});
</script>

<style>
.corps-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.corps-card:hover {
    transform: translateY(-2px);
}

.corps-card.border-primary {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.rank-row {
    transition: all 0.3s ease;
}

.stats-card {
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
{% endblock %}

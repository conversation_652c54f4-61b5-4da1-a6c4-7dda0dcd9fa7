{% extends "base.html" %}

{% block title %}الرتب والأسلاك - الإعدادات{% endblock %}

{% block page_title %}الرتب والأسلاك{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item"><a href="{{ url_for('settings') }}">الإعدادات</a></li>
<li class="breadcrumb-item active">الرتب والأسلاك</li>
{% endblock %}

{% block content %}
<div class="row">
    <!-- إدارة الأسلاك -->
    <div class="col-md-6">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-layer-group"></i> إدارة الأسلاك
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#addCorpsModal">
                        <i class="fas fa-plus"></i> إضافة سلك
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم السلك</th>
                                <th>النوع</th>
                                <th>عدد الرتب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>أسلاك خاصة بإدارة الجمارك</td>
                                <td><span class="badge badge-primary">خاص</span></td>
                                <td><span class="badge badge-info">10</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-sm" onclick="editCorps(1)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteCorps(1)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>أسلاك مشتركة</td>
                                <td><span class="badge badge-success">مشترك</span></td>
                                <td><span class="badge badge-info">9</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-sm" onclick="editCorps(2)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteCorps(2)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>عمال مهنيين</td>
                                <td><span class="badge badge-warning">مهني</span></td>
                                <td><span class="badge badge-info">3</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-sm" onclick="editCorps(3)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteCorps(3)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- إدارة الرتب -->
    <div class="col-md-6">
        <div class="card card-success">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-star"></i> إدارة الرتب
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#addRankModal">
                        <i class="fas fa-plus"></i> إضافة رتبة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- فلتر حسب السلك -->
                <div class="form-group">
                    <label for="corpsFilter">فلترة حسب السلك:</label>
                    <select class="form-control" id="corpsFilter" onchange="filterRanks()">
                        <option value="">جميع الأسلاك</option>
                        <option value="خاص">أسلاك خاصة</option>
                        <option value="مشترك">أسلاك مشتركة</option>
                        <option value="مهني">عمال مهنيين</option>
                    </select>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>الرتبة</th>
                                <th>السلك</th>
                                <th>المستوى</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="ranksTableBody">
                            <!-- الأسلاك الخاصة -->
                            <tr data-corps="خاص">
                                <td>مدير عام للجمارك</td>
                                <td><span class="badge badge-primary">خاص</span></td>
                                <td><span class="badge badge-danger">15</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editRank(1)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deleteRank(1)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr data-corps="خاص">
                                <td>مدير جهوي للجمارك</td>
                                <td><span class="badge badge-primary">خاص</span></td>
                                <td><span class="badge badge-danger">14</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editRank(2)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deleteRank(2)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr data-corps="خاص">
                                <td>مفتش رئيسي للجمارك</td>
                                <td><span class="badge badge-primary">خاص</span></td>
                                <td><span class="badge badge-warning">12</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editRank(3)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deleteRank(3)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr data-corps="خاص">
                                <td>مفتش للجمارك</td>
                                <td><span class="badge badge-primary">خاص</span></td>
                                <td><span class="badge badge-warning">11</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editRank(4)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deleteRank(4)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr data-corps="خاص">
                                <td>عون جمارك</td>
                                <td><span class="badge badge-primary">خاص</span></td>
                                <td><span class="badge badge-info">6</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editRank(5)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deleteRank(5)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>

                            <!-- الأسلاك المشتركة -->
                            <tr data-corps="مشترك">
                                <td>مدير إدارة مركزية</td>
                                <td><span class="badge badge-success">مشترك</span></td>
                                <td><span class="badge badge-danger">15</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editRank(6)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deleteRank(6)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr data-corps="مشترك">
                                <td>محاسب إداري</td>
                                <td><span class="badge badge-success">مشترك</span></td>
                                <td><span class="badge badge-info">9</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editRank(7)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deleteRank(7)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr data-corps="مشترك">
                                <td>كاتب</td>
                                <td><span class="badge badge-success">مشترك</span></td>
                                <td><span class="badge badge-info">7</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editRank(8)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deleteRank(8)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>

                            <!-- العمال المهنيين -->
                            <tr data-corps="مهني">
                                <td>عامل مهني من المستوى الثالث</td>
                                <td><span class="badge badge-warning">مهني</span></td>
                                <td><span class="badge badge-info">6</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editRank(9)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deleteRank(9)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr data-corps="مهني">
                                <td>عامل مهني من المستوى الأول</td>
                                <td><span class="badge badge-warning">مهني</span></td>
                                <td><span class="badge badge-secondary">4</span></td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-xs" onclick="editRank(10)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-xs" onclick="deleteRank(10)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Corps Modal -->
<div class="modal fade" id="addCorpsModal" tabindex="-1" role="dialog" aria-labelledby="addCorpsModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCorpsModalLabel">إضافة سلك جديد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="إغلاق">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addCorpsForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="corpsName">اسم السلك</label>
                        <input type="text" class="form-control" id="corpsName" name="name" 
                               placeholder="مثال: أسلاك خاصة بإدارة الجمارك" required>
                    </div>
                    <div class="form-group">
                        <label for="corpsType">نوع السلك</label>
                        <select class="form-control" id="corpsType" name="type" required>
                            <option value="">اختر النوع</option>
                            <option value="خاص">أسلاك خاصة</option>
                            <option value="مشترك">أسلاك مشتركة</option>
                            <option value="مهني">عمال مهنيين</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Rank Modal -->
<div class="modal fade" id="addRankModal" tabindex="-1" role="dialog" aria-labelledby="addRankModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRankModalLabel">إضافة رتبة جديدة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="إغلاق">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addRankForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="rankName">اسم الرتبة</label>
                        <input type="text" class="form-control" id="rankName" name="name" 
                               placeholder="مثال: مفتش للجمارك" required>
                    </div>
                    <div class="form-group">
                        <label for="rankCorps">السلك</label>
                        <select class="form-control" id="rankCorps" name="corps_id" required>
                            <option value="">اختر السلك</option>
                            <option value="1">أسلاك خاصة بإدارة الجمارك</option>
                            <option value="2">أسلاك مشتركة</option>
                            <option value="3">عمال مهنيين</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="rankLevel">مستوى الرتبة</label>
                        <input type="number" class="form-control" id="rankLevel" name="level" 
                               min="1" max="15" placeholder="من 1 إلى 15" required>
                        <small class="form-text text-muted">المستوى الأعلى يعني رتبة أعلى</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Filter ranks by corps
function filterRanks() {
    var filter = document.getElementById('corpsFilter').value;
    var rows = document.querySelectorAll('#ranksTableBody tr');
    
    rows.forEach(function(row) {
        if (filter === '' || row.getAttribute('data-corps') === filter) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Edit functions (placeholder)
function editCorps(corpsId) {
    alert('تعديل السلك رقم: ' + corpsId);
}

function deleteCorps(corpsId) {
    if (confirm('هل أنت متأكد من حذف هذا السلك؟ سيتم حذف جميع الرتب المرتبطة به.')) {
        alert('حذف السلك رقم: ' + corpsId);
    }
}

function editRank(rankId) {
    alert('تعديل الرتبة رقم: ' + rankId);
}

function deleteRank(rankId) {
    if (confirm('هل أنت متأكد من حذف هذه الرتبة؟')) {
        alert('حذف الرتبة رقم: ' + rankId);
    }
}

// Form submissions
$('#addCorpsForm').on('submit', function(e) {
    e.preventDefault();
    // Add AJAX call here
    alert('تم إضافة السلك بنجاح!');
    $('#addCorpsModal').modal('hide');
    location.reload();
});

$('#addRankForm').on('submit', function(e) {
    e.preventDefault();
    // Add AJAX call here
    alert('تم إضافة الرتبة بنجاح!');
    $('#addRankModal').modal('hide');
    location.reload();
});
</script>
{% endblock %}

{% extends "professional_base.html" %}

{% block title %}التقارير والإحصائيات - نظام إدارة الجمارك الجزائرية{% endblock %}

{% block page_title %}التقارير والإحصائيات{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item active">التقارير</li>
{% endblock %}

{% block content %}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">
            <i class="fas fa-chart-line mr-2"></i>
            التقارير والإحصائيات
        </h1>
        <p class="text-muted">تقارير شاملة ومفصلة حول الموارد البشرية</p>
    </div>
</div>

<!-- Quick Reports -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card report-card h-100">
            <div class="card-body text-center">
                <div class="report-icon bg-primary text-white mb-3">
                    <i class="fas fa-users fa-2x"></i>
                </div>
                <h5 class="card-title">تقرير الموظفين العام</h5>
                <p class="card-text">إحصائيات شاملة لجميع الموظفين</p>
                <button class="btn btn-primary" onclick="generateReport('employees_general')">
                    <i class="fas fa-file-pdf mr-1"></i>
                    إنشاء التقرير
                </button>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card report-card h-100">
            <div class="card-body text-center">
                <div class="report-icon bg-success text-white mb-3">
                    <i class="fas fa-chart-pie fa-2x"></i>
                </div>
                <h5 class="card-title">التوزيع الديموغرافي</h5>
                <p class="card-text">توزيع الموظفين حسب العمر والجنس</p>
                <button class="btn btn-success" onclick="generateReport('demographics')">
                    <i class="fas fa-file-excel mr-1"></i>
                    إنشاء التقرير
                </button>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card report-card h-100">
            <div class="card-body text-center">
                <div class="report-icon bg-warning text-white mb-3">
                    <i class="fas fa-building fa-2x"></i>
                </div>
                <h5 class="card-title">تقرير المديريات</h5>
                <p class="card-text">توزيع الموظفين حسب المديريات</p>
                <button class="btn btn-warning" onclick="generateReport('directorates')">
                    <i class="fas fa-file-pdf mr-1"></i>
                    إنشاء التقرير
                </button>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card report-card h-100">
            <div class="card-body text-center">
                <div class="report-icon bg-info text-white mb-3">
                    <i class="fas fa-medal fa-2x"></i>
                </div>
                <h5 class="card-title">تقرير الرتب</h5>
                <p class="card-text">توزيع الموظفين حسب الرتب والأسلاك</p>
                <button class="btn btn-info" onclick="generateReport('ranks')">
                    <i class="fas fa-file-excel mr-1"></i>
                    إنشاء التقرير
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Custom Reports -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter mr-2"></i>
                    تقرير مخصص
                </h5>
            </div>
            <div class="card-body">
                <form id="customReportForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="reportType">نوع التقرير</label>
                                <select class="form-control" id="reportType">
                                    <option value="">اختر نوع التقرير</option>
                                    <option value="detailed">تقرير مفصل</option>
                                    <option value="summary">تقرير موجز</option>
                                    <option value="statistical">تقرير إحصائي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="reportFormat">تنسيق التقرير</label>
                                <select class="form-control" id="reportFormat">
                                    <option value="pdf">PDF</option>
                                    <option value="excel">Excel</option>
                                    <option value="word">Word</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="filterDirectorate">تصفية حسب المديرية</label>
                                <select class="form-control" id="filterDirectorate">
                                    <option value="">جميع المديريات</option>
                                    <option value="1">المديرية العامة للجمارك</option>
                                    <option value="2">المديرية الجهوية - الجزائر</option>
                                    <option value="3">المديرية الجهوية - وهران</option>
                                    <option value="4">المديرية الجهوية - قسنطينة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="filterStatus">تصفية حسب الحالة</label>
                                <select class="form-control" id="filterStatus">
                                    <option value="">جميع الحالات</option>
                                    <option value="نشط">نشط</option>
                                    <option value="في إجازة">في إجازة</option>
                                    <option value="معلق">معلق</option>
                                    <option value="متقاعد">متقاعد</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="dateFrom">من تاريخ</label>
                                <input type="date" class="form-control" id="dateFrom">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="dateTo">إلى تاريخ</label>
                                <input type="date" class="form-control" id="dateTo">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>الحقول المطلوبة</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="field_personal" checked>
                                    <label class="form-check-label" for="field_personal">المعلومات الشخصية</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="field_contact" checked>
                                    <label class="form-check-label" for="field_contact">معلومات الاتصال</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="field_employment" checked>
                                    <label class="form-check-label" for="field_employment">معلومات التوظيف</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="field_family">
                                    <label class="form-check-label" for="field_family">المعلومات العائلية</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="field_official">
                                    <label class="form-check-label" for="field_official">المعلومات الرسمية</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="field_statistics" checked>
                                    <label class="form-check-label" for="field_statistics">الإحصائيات</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="button" class="btn btn-primary btn-lg" onclick="generateCustomReport()">
                            <i class="fas fa-cogs mr-2"></i>
                            إنشاء التقرير المخصص
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Report History -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history mr-2"></i>
                    التقارير الأخيرة
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">تقرير الموظفين العام</h6>
                            <small class="text-muted">اليوم 14:30</small>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" onclick="downloadReport('report1.pdf')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">التوزيع الديموغرافي</h6>
                            <small class="text-muted">أمس 16:45</small>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-success" onclick="downloadReport('report2.xlsx')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">تقرير المديريات</h6>
                            <small class="text-muted">أمس 10:20</small>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-warning" onclick="downloadReport('report3.pdf')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <a href="#" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list mr-1"></i>
                        عرض جميع التقارير
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <h4 class="text-primary">3</h4>
                        <small class="text-muted">موظفين جدد هذا الشهر</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success">95%</h4>
                        <small class="text-muted">معدل الحضور</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">12</h4>
                        <small class="text-muted">تقارير هذا الشهر</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">8</h4>
                        <small class="text-muted">مديريات نشطة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function generateReport(reportType) {
    // إظهار مؤشر التحميل
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> جاري الإنشاء...';
    button.disabled = true;
    
    // محاكاة إنشاء التقرير
    setTimeout(function() {
        button.innerHTML = originalText;
        button.disabled = false;
        
        // إظهار رسالة نجاح
        alert(`تم إنشاء تقرير ${getReportName(reportType)} بنجاح!\nسيتم تحميله تلقائياً.`);
        
        // محاكاة تحميل الملف
        downloadReport(`${reportType}_${new Date().getTime()}.pdf`);
    }, 2000);
}

function generateCustomReport() {
    const reportType = $('#reportType').val();
    const reportFormat = $('#reportFormat').val();
    
    if (!reportType) {
        alert('يرجى اختيار نوع التقرير');
        return;
    }
    
    // جمع البيانات المحددة
    const filters = {
        directorate: $('#filterDirectorate').val(),
        status: $('#filterStatus').val(),
        dateFrom: $('#dateFrom').val(),
        dateTo: $('#dateTo').val()
    };
    
    const fields = [];
    if ($('#field_personal').is(':checked')) fields.push('personal');
    if ($('#field_contact').is(':checked')) fields.push('contact');
    if ($('#field_employment').is(':checked')) fields.push('employment');
    if ($('#field_family').is(':checked')) fields.push('family');
    if ($('#field_official').is(':checked')) fields.push('official');
    if ($('#field_statistics').is(':checked')) fields.push('statistics');
    
    // إظهار مؤشر التحميل
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> جاري الإنشاء...';
    button.disabled = true;
    
    // محاكاة إنشاء التقرير
    setTimeout(function() {
        button.innerHTML = originalText;
        button.disabled = false;
        
        alert(`تم إنشاء التقرير المخصص بنجاح!\nالنوع: ${reportType}\nالتنسيق: ${reportFormat}\nالحقول: ${fields.length} حقل`);
        
        // محاكاة تحميل الملف
        downloadReport(`custom_report_${new Date().getTime()}.${reportFormat}`);
    }, 3000);
}

function downloadReport(filename) {
    // محاكاة تحميل الملف
    console.log(`تحميل الملف: ${filename}`);
    
    // في التطبيق الحقيقي، سيتم إنشاء رابط تحميل فعلي
    const link = document.createElement('a');
    link.href = '#';
    link.download = filename;
    link.click();
}

function getReportName(reportType) {
    const names = {
        'employees_general': 'الموظفين العام',
        'demographics': 'التوزيع الديموغرافي',
        'directorates': 'المديريات',
        'ranks': 'الرتب والأسلاك'
    };
    return names[reportType] || reportType;
}

$(document).ready(function() {
    // تأثيرات تحويم على بطاقات التقارير
    $('.report-card').hover(
        function() {
            $(this).addClass('shadow-lg');
            $(this).find('.report-icon').addClass('animate__animated animate__pulse');
        },
        function() {
            $(this).removeClass('shadow-lg');
            $(this).find('.report-icon').removeClass('animate__animated animate__pulse');
        }
    );
    
    // تعيين التاريخ الحالي كحد أقصى
    const today = new Date().toISOString().split('T')[0];
    $('#dateFrom, #dateTo').attr('max', today);
    
    // تعيين تاريخ افتراضي (آخر شهر)
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    $('#dateFrom').val(lastMonth.toISOString().split('T')[0]);
    $('#dateTo').val(today);
});
</script>

<style>
.report-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.report-card:hover {
    transform: translateY(-5px);
    border-color: #007bff;
}

.report-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.list-group-item {
    border: none;
    border-bottom: 1px solid #dee2e6;
}

.list-group-item:last-child {
    border-bottom: none;
}

.form-check {
    margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
    .report-card {
        margin-bottom: 1rem;
    }
}
</style>
{% endblock %}

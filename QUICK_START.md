# دليل البدء السريع
## Quick Start Guide

## 🚀 تشغيل التطبيق في 3 خطوات

### الخطوة 1: تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### الخطوة 2: تشغيل التطبيق
```bash
python simple_app.py
```

### الخطوة 3: فتح المتصفح
```
http://localhost:5000
```

---

## 📋 الصفحات المتاحة

### 🏠 الصفحة الرئيسية
- **الرابط:** `/`
- **الوصف:** لوحة التحكم مع الإحصائيات
- **المميزات:** 
  - إحصائيات سريعة للموظفين
  - رسوم بيانية تفاعلية
  - قائمة المهام
  - التقويم

### 👥 إدارة الموظفين
- **قائمة الموظفين:** `/employees`
- **إضافة موظف:** `/employee/add`
- **تفاصيل الموظف:** `/employee/<id>`
- **المميزات:**
  - بحث وتصفية متقدم
  - عرض تفصيلي للبيانات
  - نموذج إضافة شامل

### ⚙️ الإعدادات
- **الإعدادات الرئيسية:** `/settings`
- **التقسيم الإداري:** `/settings/administrative_divisions`
- **الرتب:** `/settings/ranks`
- **الوظائف:** `/settings/positions`

---

## 🎨 مميزات الواجهة

### ✅ دعم اللغة العربية
- تخطيط RTL كامل
- خطوط عربية جميلة
- محتوى باللغة العربية

### ✅ تصميم متجاوب
- يعمل على جميع الشاشات
- تحسين للهواتف والأجهزة اللوحية
- واجهة سهلة الاستخدام

### ✅ ألوان مخصصة للجمارك
- ألوان تتماشى مع هوية الجمارك الجزائرية
- تدرجات لونية احترافية
- تأثيرات بصرية جذابة

---

## 🔧 إعدادات سريعة

### تغيير المنفذ
```python
# في ملف simple_app.py
app.run(debug=True, host='0.0.0.0', port=5001)  # بدلاً من 5000
```

### تفعيل وضع التطوير
```python
app.run(debug=True)  # لإعادة التحميل التلقائي
```

### إضافة بيانات وهمية
البيانات الوهمية موجودة بالفعل في التطبيق للاختبار

---

## 🛠️ استكشاف الأخطاء السريع

### المشكلة: لا تظهر الواجهة بشكل صحيح
**الحل:**
1. تأكد من تحميل ملفات CSS
2. امسح ذاكرة التخزين المؤقت للمتصفح (Ctrl+F5)
3. تحقق من اتصال الإنترنت (للمكتبات الخارجية)

### المشكلة: خطأ في تشغيل التطبيق
**الحل:**
1. تأكد من تثبيت Python 3.8+
2. تأكد من تثبيت جميع المتطلبات
3. تحقق من عدم استخدام المنفذ 5000

### المشكلة: البيانات لا تظهر
**الحل:**
1. التطبيق المبسط يستخدم بيانات وهمية
2. للبيانات الحقيقية استخدم `python run.py`
3. تأكد من تهيئة قاعدة البيانات

---

## 📱 اختبار سريع

### اختبار الصفحات الأساسية:
1. ✅ الصفحة الرئيسية - `http://localhost:5000`
2. ✅ قائمة الموظفين - `http://localhost:5000/employees`
3. ✅ إضافة موظف - `http://localhost:5000/employee/add`
4. ✅ الإعدادات - `http://localhost:5000/settings`

### اختبار الوظائف:
1. ✅ البحث في قائمة الموظفين
2. ✅ التصفية حسب الحالة
3. ✅ عرض تفاصيل الموظف
4. ✅ التنقل بين الصفحات

---

## 🎯 نصائح للاستخدام الأمثل

### للمطورين:
- استخدم `python simple_app.py` للاختبار السريع
- استخدم `python run.py` للتطوير الكامل
- راجع ملف `PROJECT_SUMMARY.md` للتفاصيل الكاملة

### للمستخدمين:
- استخدم البحث للعثور على الموظفين بسرعة
- استفد من التصفية لتضييق النتائج
- راجع الإحصائيات في الصفحة الرئيسية

### للإدارة:
- راقب الإحصائيات في لوحة التحكم
- استخدم التقارير لاتخاذ القرارات
- حدث البيانات بانتظام

---

## 📞 الدعم والمساعدة

### في حالة وجود مشاكل:
1. راجع ملف `INSTALLATION.md` للتثبيت المفصل
2. راجع ملف `README.md` للوثائق الكاملة
3. تحقق من سجلات الأخطاء في وحدة التحكم
4. تأكد من تحديث المتطلبات

### للتطوير المتقدم:
- راجع ملف `models.py` لفهم هيكل قاعدة البيانات
- راجع ملف `forms.py` لفهم نماذج الإدخال
- راجع مجلد `templates/` لتخصيص الواجهة

---

## 🎉 مبروك!

إذا وصلت إلى هنا، فقد نجحت في تشغيل برنامج تسيير مستخدمي الجمارك الجزائرية!

**التطبيق جاهز للاستخدام والتطوير** 🚀

---

**ملاحظة:** هذا الدليل للبدء السريع. للحصول على الوثائق الكاملة، راجع الملفات الأخرى في المشروع.

from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import <PERSON>Field, IntegerField, SelectField, DateField, TextAreaField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, Email, Optional, NumberRange, ValidationError
from wtforms.widgets import TextArea
from datetime import datetime, date
import re
# Models will be imported in __init__ method

class EmployeeForm(FlaskForm):
    """نموذج إضافة/تعديل الموظف"""
    
    # البيانات الأساسية
    registration_number = StringField('رقم التسجيل', validators=[
        DataRequired(message='رقم التسجيل مطلوب'),
        Length(min=6, max=6, message='رقم التسجيل يجب أن يكون 6 أرقام')
    ])
    
    last_name = StringField('اللقب', validators=[
        DataRequired(message='اللقب مطلوب'),
        Length(min=2, max=100, message='اللقب يجب أن يكون بين 2 و 100 حرف')
    ])
    
    first_name = StringField('الاسم', validators=[
        DataRequired(message='الاسم مطلوب'),
        Length(min=2, max=100, message='الاسم يجب أن يكون بين 2 و 100 حرف')
    ])
    
    nom_fr = StringField('Nom (بالفرنسية)', validators=[
        Optional(),
        Length(max=100, message='الاسم بالفرنسية لا يجب أن يتجاوز 100 حرف')
    ])
    
    prenom_fr = StringField('Prénom (بالفرنسية)', validators=[
        Optional(),
        Length(max=100, message='الاسم بالفرنسية لا يجب أن يتجاوز 100 حرف')
    ])
    
    gender = SelectField('الجنس', choices=[
        ('ذكر', 'ذكر'),
        ('أنثى', 'أنثى')
    ], validators=[DataRequired(message='الجنس مطلوب')])
    
    birth_date = DateField('تاريخ الميلاد', validators=[
        DataRequired(message='تاريخ الميلاد مطلوب')
    ])
    
    birth_wilaya_id = SelectField('ولاية الميلاد', coerce=int, validators=[
        DataRequired(message='ولاية الميلاد مطلوبة')
    ])
    
    birth_commune_id = SelectField('بلدية الميلاد', coerce=int, validators=[
        DataRequired(message='بلدية الميلاد مطلوبة')
    ])
    
    marital_status = SelectField('الحالة العائلية', choices=[
        ('أعزب', 'أعزب'),
        ('متزوج', 'متزوج'),
        ('مطلق', 'مطلق'),
        ('أرمل', 'أرمل')
    ], validators=[DataRequired(message='الحالة العائلية مطلوبة')])
    
    children_count = IntegerField('عدد الأبناء', validators=[
        Optional(),
        NumberRange(min=0, max=20, message='عدد الأبناء يجب أن يكون بين 0 و 20')
    ], default=0)
    
    dependents_count = IntegerField('عدد الأشخاص المتكفل بهم', validators=[
        Optional(),
        NumberRange(min=0, max=20, message='عدد المتكفل بهم يجب أن يكون بين 0 و 20')
    ], default=0)
    
    blood_type = SelectField('زمرة الدم', choices=[
        ('', 'اختر زمرة الدم'),
        ('A+', 'A+'),
        ('A-', 'A-'),
        ('B+', 'B+'),
        ('B-', 'B-'),
        ('AB+', 'AB+'),
        ('AB-', 'AB-'),
        ('O+', 'O+'),
        ('O-', 'O-')
    ], validators=[Optional()])
    
    photo = FileField('صورة الموظف', validators=[
        Optional(),
        FileAllowed(['jpg', 'jpeg', 'png', 'gif'], 'الصور فقط!')
    ])
    
    # بيانات الاتصال
    phone1 = StringField('رقم الهاتف 1', validators=[
        Optional(),
        Length(max=20, message='رقم الهاتف لا يجب أن يتجاوز 20 رقم')
    ])
    
    phone2 = StringField('رقم الهاتف 2', validators=[
        Optional(),
        Length(max=20, message='رقم الهاتف لا يجب أن يتجاوز 20 رقم')
    ])
    
    email = StringField('البريد الإلكتروني', validators=[
        Optional(),
        Email(message='البريد الإلكتروني غير صحيح')
    ])
    
    primary_address = TextAreaField('العنوان الرئيسي', validators=[Optional()])
    secondary_address = TextAreaField('العنوان الثانوي', validators=[Optional()])
    emergency_contact_name = StringField('اسم الشخص المتصل به في حالة الضرورة', validators=[Optional()])
    emergency_contact_address = TextAreaField('عنوان الشخص المتصل به في حالة الضرورة', validators=[Optional()])
    
    # البيانات المهنية
    status = SelectField('حالة الموظف', choices=[
        ('نشط', 'نشط'),
        ('تحويل', 'تحويل'),
        ('موقف', 'موقف'),
        ('استيداع', 'استيداع'),
        ('منتدب', 'منتدب'),
        ('متوفي', 'متوفي'),
        ('مفصول', 'مفصول'),
        ('مستقيل', 'مستقيل')
    ], validators=[DataRequired(message='حالة الموظف مطلوبة')])
    
    corps_id = SelectField('السلك', coerce=int, validators=[
        DataRequired(message='السلك مطلوب')
    ])
    
    current_rank_id = SelectField('الرتبة الحالية', coerce=int, validators=[
        DataRequired(message='الرتبة الحالية مطلوبة')
    ])
    
    rank_promotion_date = DateField('تاريخ الترقية في الرتبة الحالية', validators=[Optional()])
    
    current_position_id = SelectField('الوظيفة الحالية', coerce=int, validators=[
        DataRequired(message='الوظيفة الحالية مطلوبة')
    ])
    
    position_assignment_date = DateField('تاريخ التعيين في الوظيفة الحالية', validators=[Optional()])
    
    directorate_id = SelectField('المديرية', coerce=int, validators=[
        DataRequired(message='المديرية مطلوبة')
    ])
    
    service_id = SelectField('المصلحة', coerce=int, validators=[
        DataRequired(message='المصلحة مطلوبة')
    ])
    
    assignment_location = StringField('مكان التعيين', validators=[Optional()])
    
    hiring_date = DateField('تاريخ التوظيف/الدخول في إدارة الجمارك', validators=[
        DataRequired(message='تاريخ التوظيف مطلوب')
    ])
    
    hiring_rank_id = SelectField('رتبة التوظيف', coerce=int, validators=[
        DataRequired(message='رتبة التوظيف مطلوبة')
    ])
    
    # الوثائق والأرقام
    social_security_number = StringField('رقم الضمان الاجتماعي', validators=[
        DataRequired(message='رقم الضمان الاجتماعي مطلوب'),
        Length(min=15, max=15, message='رقم الضمان الاجتماعي يجب أن يكون 15 رقم')
    ])
    
    postal_account_number = StringField('رقم الحساب الجاري البريدي', validators=[
        Optional(),
        Length(min=10, max=10, message='رقم الحساب الجاري البريدي يجب أن يكون 10 أرقام')
    ])
    
    professional_card_number = StringField('رقم البطاقة المهنية', validators=[Optional()])
    professional_card_issue_date = DateField('تاريخ صدور البطاقة المهنية', validators=[Optional()])
    
    national_id_number = StringField('رقم بطاقة التعريف الوطنية', validators=[
        DataRequired(message='رقم بطاقة التعريف الوطنية مطلوب'),
        Length(min=18, max=18, message='رقم بطاقة التعريف الوطنية يجب أن يكون 18 رقم')
    ])
    
    national_id_issue_date = DateField('تاريخ صدور بطاقة التعريف الوطنية', validators=[Optional()])
    national_id_issue_place_id = SelectField('مكان صدور بطاقة التعريف الوطنية', coerce=int, validators=[Optional()])
    
    driving_license_number = StringField('رقم رخصة السياقة', validators=[Optional()])
    driving_license_category = StringField('صنف رخصة السياقة', validators=[Optional()])
    driving_license_issue_date = DateField('تاريخ صدور رخصة السياقة', validators=[Optional()])
    driving_license_issue_place_id = SelectField('مكان صدور رخصة السياقة', coerce=int, validators=[Optional()])
    
    mutual_card_number = StringField('رقم بطاقة التعاضدية', validators=[Optional()])
    mutual_card_issue_date = DateField('تاريخ صدور بطاقة التعاضدية', validators=[Optional()])
    
    practiced_sport = StringField('الرياضة الممارسة', validators=[Optional()])
    
    submit = SubmitField('حفظ')
    
    def __init__(self, *args, **kwargs):
        super(EmployeeForm, self).__init__(*args, **kwargs)

        # Import models here to avoid circular imports
        from models import Wilaya, Commune, Corps, Rank, Position, Directorate, Service

        # تحديث خيارات القوائم المنسدلة
        self.birth_wilaya_id.choices = [(w.id, w.name_ar) for w in Wilaya.query.all()]
        self.birth_commune_id.choices = [(c.id, c.name_ar) for c in Commune.query.all()]
        self.corps_id.choices = [(c.id, c.name) for c in Corps.query.all()]
        self.current_rank_id.choices = [(r.id, r.name) for r in Rank.query.all()]
        self.hiring_rank_id.choices = [(r.id, r.name) for r in Rank.query.all()]
        self.current_position_id.choices = [(p.id, p.name) for p in Position.query.all()]
        self.directorate_id.choices = [(d.id, d.name) for d in Directorate.query.all()]
        self.service_id.choices = [(s.id, s.name) for s in Service.query.all()]
        self.national_id_issue_place_id.choices = [('', 'اختر المكان')] + [(c.id, c.name_ar) for c in Commune.query.all()]
        self.driving_license_issue_place_id.choices = [('', 'اختر المكان')] + [(c.id, c.name_ar) for c in Commune.query.all()]
    
    def validate_registration_number(self, field):
        """التحقق من رقم التسجيل"""
        if not field.data.isdigit():
            raise ValidationError('رقم التسجيل يجب أن يحتوي على أرقام فقط')

        # التحقق من عدم التكرار
        from models import Employee
        existing = Employee.query.filter_by(registration_number=field.data).first()
        if existing and (not hasattr(self, 'obj') or existing.id != self.obj.id):
            raise ValidationError('رقم التسجيل موجود مسبقاً')
    
    def validate_birth_date(self, field):
        """التحقق من تاريخ الميلاد"""
        if field.data:
            today = date.today()
            age = today.year - field.data.year - ((today.month, today.day) < (field.data.month, field.data.day))
            
            if age < 19:
                raise ValidationError('عمر الموظف يجب أن يكون 19 سنة على الأقل')
            elif age > 65:
                raise ValidationError('عمر الموظف يجب أن لا يتجاوز 65 سنة')
    
    def validate_social_security_number(self, field):
        """التحقق من رقم الضمان الاجتماعي"""
        if field.data:
            if not field.data.isdigit():
                raise ValidationError('رقم الضمان الاجتماعي يجب أن يحتوي على أرقام فقط')

            # التحقق من عدم التكرار
            from models import Employee
            existing = Employee.query.filter_by(social_security_number=field.data).first()
            if existing and (not hasattr(self, 'obj') or existing.id != self.obj.id):
                raise ValidationError('رقم الضمان الاجتماعي موجود مسبقاً')

    def validate_national_id_number(self, field):
        """التحقق من رقم بطاقة التعريف الوطنية"""
        if field.data:
            if not field.data.isdigit():
                raise ValidationError('رقم بطاقة التعريف الوطنية يجب أن يحتوي على أرقام فقط')

            # التحقق من عدم التكرار
            from models import Employee
            existing = Employee.query.filter_by(national_id_number=field.data).first()
            if existing and (not hasattr(self, 'obj') or existing.id != self.obj.id):
                raise ValidationError('رقم بطاقة التعريف الوطنية موجود مسبقاً')
    
    def validate_postal_account_number(self, field):
        """التحقق من رقم الحساب الجاري البريدي"""
        if field.data and not field.data.isdigit():
            raise ValidationError('رقم الحساب الجاري البريدي يجب أن يحتوي على أرقام فقط')

class CertificateForm(FlaskForm):
    """نموذج إضافة/تعديل الشهادات"""

    certificate_type = SelectField('نوع الشهادة', choices=[
        ('دكتوراه', 'دكتوراه'),
        ('ماستر', 'ماستر'),
        ('ليسانس', 'ليسانس'),
        ('تقني سامي', 'تقني سامي'),
        ('بكالوريا', 'بكالوريا'),
        ('تعليم متوسط', 'تعليم متوسط'),
        ('تعليم ابتدائي', 'تعليم ابتدائي')
    ], validators=[DataRequired(message='نوع الشهادة مطلوب')])

    specialization = StringField('التخصص', validators=[
        DataRequired(message='التخصص مطلوب'),
        Length(min=2, max=200, message='التخصص يجب أن يكون بين 2 و 200 حرف')
    ])

    year_obtained = IntegerField('سنة المنح', validators=[
        DataRequired(message='سنة المنح مطلوبة'),
        NumberRange(min=1950, max=2030, message='سنة المنح يجب أن تكون بين 1950 و 2030')
    ])

    institution = StringField('المؤسسة المانحة', validators=[
        DataRequired(message='المؤسسة المانحة مطلوبة'),
        Length(min=2, max=200, message='المؤسسة المانحة يجب أن تكون بين 2 و 200 حرف')
    ])

    submit = SubmitField('حفظ')

class TrainingForm(FlaskForm):
    """نموذج إضافة/تعديل التكوين"""

    subject = StringField('موضوع التكوين', validators=[
        DataRequired(message='موضوع التكوين مطلوب'),
        Length(min=2, max=200, message='موضوع التكوين يجب أن يكون بين 2 و 200 حرف')
    ])

    duration = StringField('المدة الزمنية', validators=[
        DataRequired(message='المدة الزمنية مطلوبة'),
        Length(min=1, max=100, message='المدة الزمنية يجب أن تكون بين 1 و 100 حرف')
    ])

    start_date = DateField('تاريخ بداية التكوين', validators=[
        DataRequired(message='تاريخ بداية التكوين مطلوب')
    ])

    end_date = DateField('تاريخ نهاية التكوين', validators=[
        DataRequired(message='تاريخ نهاية التكوين مطلوب')
    ])

    submit = SubmitField('حفظ')

    def validate_end_date(self, field):
        """التحقق من أن تاريخ النهاية بعد تاريخ البداية"""
        if field.data and self.start_date.data and field.data <= self.start_date.data:
            raise ValidationError('تاريخ نهاية التكوين يجب أن يكون بعد تاريخ البداية')

class EmployeeLanguageForm(FlaskForm):
    """نموذج إضافة/تعديل لغات الموظف"""

    language_id = SelectField('اللغة', coerce=int, validators=[
        DataRequired(message='اللغة مطلوبة')
    ])

    can_write = BooleanField('يستطيع الكتابة')

    writing_level = SelectField('مستوى الكتابة', choices=[
        ('', 'اختر المستوى'),
        ('ممتاز', 'ممتاز'),
        ('جيد', 'جيد'),
        ('متوسط', 'متوسط')
    ], validators=[Optional()])

    can_read = BooleanField('يستطيع القراءة')

    reading_level = SelectField('مستوى القراءة', choices=[
        ('', 'اختر المستوى'),
        ('ممتاز', 'ممتاز'),
        ('جيد', 'جيد'),
        ('متوسط', 'متوسط')
    ], validators=[Optional()])

    submit = SubmitField('حفظ')

    def __init__(self, *args, **kwargs):
        super(EmployeeLanguageForm, self).__init__(*args, **kwargs)

        # Import models here to avoid circular imports
        from models import Language

        # تحديث خيارات اللغات
        self.language_id.choices = [(l.id, l.name) for l in Language.query.all()]

class TransferForm(FlaskForm):
    """نموذج إضافة/تعديل التحويلات والتنقلات"""

    directorate_id = SelectField('المديرية', coerce=int, validators=[
        DataRequired(message='المديرية مطلوبة')
    ])

    service_id = SelectField('المصلحة', coerce=int, validators=[
        DataRequired(message='المصلحة مطلوبة')
    ])

    assignment_location = StringField('مكان التعيين', validators=[
        DataRequired(message='مكان التعيين مطلوب'),
        Length(min=2, max=200, message='مكان التعيين يجب أن يكون بين 2 و 200 حرف')
    ])

    installation_date = DateField('تاريخ التنصيب', validators=[
        DataRequired(message='تاريخ التنصيب مطلوب')
    ])

    end_date = DateField('تاريخ انهاء المهام', validators=[Optional()])

    position_id = SelectField('الوظيفة', coerce=int, validators=[
        DataRequired(message='الوظيفة مطلوبة')
    ])

    decision_number = StringField('رقم المقرر', validators=[
        Optional(),
        Length(max=100, message='رقم المقرر لا يجب أن يتجاوز 100 حرف')
    ])

    decision_date = DateField('تاريخ المقرر', validators=[Optional()])

    submit = SubmitField('حفظ')

    def __init__(self, *args, **kwargs):
        super(TransferForm, self).__init__(*args, **kwargs)

        # Import models here to avoid circular imports
        from models import Directorate, Service, Position

        # تحديث خيارات القوائم المنسدلة
        self.directorate_id.choices = [(d.id, d.name) for d in Directorate.query.all()]
        self.service_id.choices = [(s.id, s.name) for s in Service.query.all()]
        self.position_id.choices = [(p.id, p.name) for p in Position.query.all()]

    def validate_end_date(self, field):
        """التحقق من أن تاريخ انهاء المهام بعد تاريخ التنصيب"""
        if field.data and self.installation_date.data and field.data <= self.installation_date.data:
            raise ValidationError('تاريخ انهاء المهام يجب أن يكون بعد تاريخ التنصيب')

class AnnualLeaveForm(FlaskForm):
    """نموذج إضافة/تعديل العطل السنوية"""

    year = IntegerField('سنة العطلة', validators=[
        DataRequired(message='سنة العطلة مطلوبة'),
        NumberRange(min=2000, max=2050, message='سنة العطلة يجب أن تكون بين 2000 و 2050')
    ])

    days_count = IntegerField('عدد أيام العطلة', validators=[
        DataRequired(message='عدد أيام العطلة مطلوب'),
        NumberRange(min=1, max=50, message='عدد أيام العطلة يجب أن يكون بين 1 و 50')
    ])

    start_date = DateField('تاريخ بداية العطلة', validators=[
        DataRequired(message='تاريخ بداية العطلة مطلوب')
    ])

    destination = StringField('المكان المقصود', validators=[
        Optional(),
        Length(max=200, message='المكان المقصود لا يجب أن يتجاوز 200 حرف')
    ])

    decision_number = StringField('رقم مقرر العطلة', validators=[
        Optional(),
        Length(max=100, message='رقم مقرر العطلة لا يجب أن يتجاوز 100 حرف')
    ])

    decision_date = DateField('تاريخ مقرر العطلة', validators=[Optional()])

    submit = SubmitField('حفظ')

class SickLeaveForm(FlaskForm):
    """نموذج إضافة/تعديل العطل المرضية"""

    leave_type = StringField('نوع العطلة المرضية', validators=[
        DataRequired(message='نوع العطلة المرضية مطلوب'),
        Length(min=2, max=100, message='نوع العطلة المرضية يجب أن يكون بين 2 و 100 حرف')
    ])

    start_date = DateField('تاريخ بداية العطلة', validators=[
        DataRequired(message='تاريخ بداية العطلة مطلوب')
    ])

    days_count = IntegerField('عدد أيام العطلة', validators=[
        DataRequired(message='عدد أيام العطلة مطلوب'),
        NumberRange(min=1, max=180, message='عدد أيام العطلة يجب أن يكون بين 1 و 180')
    ])

    is_indexed = BooleanField('مؤشرة')

    internal_medical_control = BooleanField('الرقابة الطبية الداخلية')

    doctor_opinion = SelectField('رأي الطبيب المستشار', choices=[
        ('', 'اختر الرأي'),
        ('مقبولة', 'مقبولة'),
        ('غير مقبولة', 'غير مقبولة')
    ], validators=[Optional()])

    deduction_number = StringField('رقم الخصم', validators=[
        Optional(),
        Length(max=100, message='رقم الخصم لا يجب أن يتجاوز 100 حرف')
    ])

    deduction_date = DateField('تاريخ الخصم', validators=[Optional()])

    submit = SubmitField('حفظ')

class OtherLeaveForm(FlaskForm):
    """نموذج إضافة/تعديل العطل الأخرى"""

    leave_type = SelectField('نوع العطلة', choices=[
        ('استثنائية', 'استثنائية'),
        ('بدون راتب', 'بدون راتب'),
        ('أمومة', 'أمومة'),
        ('حج', 'حج'),
        ('زواج', 'زواج'),
        ('وفاة', 'وفاة')
    ], validators=[DataRequired(message='نوع العطلة مطلوب')])

    start_date = DateField('تاريخ بداية العطلة', validators=[
        DataRequired(message='تاريخ بداية العطلة مطلوب')
    ])

    days_count = IntegerField('عدد أيام العطلة', validators=[
        DataRequired(message='عدد أيام العطلة مطلوب'),
        NumberRange(min=1, max=365, message='عدد أيام العطلة يجب أن يكون بين 1 و 365')
    ])

    reason = TextAreaField('سبب العطلة', validators=[
        DataRequired(message='سبب العطلة مطلوب'),
        Length(min=5, max=500, message='سبب العطلة يجب أن يكون بين 5 و 500 حرف')
    ])

    destination = StringField('المكان المقصود', validators=[
        Optional(),
        Length(max=200, message='المكان المقصود لا يجب أن يتجاوز 200 حرف')
    ])

    decision_number = StringField('رقم مقرر العطلة', validators=[
        Optional(),
        Length(max=100, message='رقم مقرر العطلة لا يجب أن يتجاوز 100 حرف')
    ])

    decision_date = DateField('تاريخ مقرر العطلة', validators=[Optional()])

    salary_deduction = BooleanField('الخصم من الراتب')

    deduction_number = StringField('رقم الخصم', validators=[
        Optional(),
        Length(max=100, message='رقم الخصم لا يجب أن يتجاوز 100 حرف')
    ])

    deduction_date = DateField('تاريخ الخصم', validators=[Optional()])

    submit = SubmitField('حفظ')

class SpouseForm(FlaskForm):
    """نموذج إضافة/تعديل بيانات الزوجة"""

    first_name = StringField('الاسم', validators=[
        DataRequired(message='الاسم مطلوب'),
        Length(min=2, max=100, message='الاسم يجب أن يكون بين 2 و 100 حرف')
    ])

    last_name = StringField('اللقب', validators=[
        DataRequired(message='اللقب مطلوب'),
        Length(min=2, max=100, message='اللقب يجب أن يكون بين 2 و 100 حرف')
    ])

    birth_date = DateField('تاريخ الميلاد', validators=[
        DataRequired(message='تاريخ الميلاد مطلوب')
    ])

    birth_wilaya_id = SelectField('ولاية الميلاد', coerce=int, validators=[
        DataRequired(message='ولاية الميلاد مطلوبة')
    ])

    birth_commune_id = SelectField('بلدية الميلاد', coerce=int, validators=[
        DataRequired(message='بلدية الميلاد مطلوبة')
    ])

    job = StringField('الوظيفة', validators=[
        Optional(),
        Length(max=200, message='الوظيفة لا يجب أن تتجاوز 200 حرف')
    ])

    workplace = StringField('مكان العمل', validators=[
        Optional(),
        Length(max=200, message='مكان العمل لا يجب أن يتجاوز 200 حرف')
    ])

    submit = SubmitField('حفظ')

    def __init__(self, *args, **kwargs):
        super(SpouseForm, self).__init__(*args, **kwargs)

        # Import models here to avoid circular imports
        from models import Wilaya, Commune

        # تحديث خيارات القوائم المنسدلة
        self.birth_wilaya_id.choices = [(w.id, w.name_ar) for w in Wilaya.query.all()]
        self.birth_commune_id.choices = [(c.id, c.name_ar) for c in Commune.query.all()]

class ChildForm(FlaskForm):
    """نموذج إضافة/تعديل بيانات الأولاد"""

    last_name = StringField('اللقب', validators=[
        DataRequired(message='اللقب مطلوب'),
        Length(min=2, max=100, message='اللقب يجب أن يكون بين 2 و 100 حرف')
    ])

    first_name = StringField('الاسم', validators=[
        DataRequired(message='الاسم مطلوب'),
        Length(min=2, max=100, message='الاسم يجب أن يكون بين 2 و 100 حرف')
    ])

    gender = SelectField('الجنس', choices=[
        ('ذكر', 'ذكر'),
        ('أنثى', 'أنثى')
    ], validators=[DataRequired(message='الجنس مطلوب')])

    is_student = BooleanField('متمدرس')

    education_level = StringField('المستوى الدراسي', validators=[
        Optional(),
        Length(max=100, message='المستوى الدراسي لا يجب أن يتجاوز 100 حرف')
    ])

    school_institution = StringField('مؤسسة التمدرس', validators=[
        Optional(),
        Length(max=200, message='مؤسسة التمدرس لا يجب أن تتجاوز 200 حرف')
    ])

    submit = SubmitField('حفظ')

class PunishmentForm(FlaskForm):
    """نموذج إضافة/تعديل العقوبات"""

    degree_id = SelectField('درجة العقوبة', coerce=int, validators=[
        DataRequired(message='درجة العقوبة مطلوبة')
    ])

    type_id = SelectField('تحديد العقوبة', coerce=int, validators=[
        DataRequired(message='تحديد العقوبة مطلوب')
    ])

    reason = TextAreaField('سبب العقوبة', validators=[
        DataRequired(message='سبب العقوبة مطلوب'),
        Length(min=10, max=1000, message='سبب العقوبة يجب أن يكون بين 10 و 1000 حرف')
    ])

    decision_number = StringField('رقم مقرر العقوبة', validators=[
        Optional(),
        Length(max=100, message='رقم مقرر العقوبة لا يجب أن يتجاوز 100 حرف')
    ])

    decision_date = DateField('تاريخ مقرر العقوبة', validators=[
        DataRequired(message='تاريخ مقرر العقوبة مطلوب')
    ])

    submit = SubmitField('حفظ')

    def __init__(self, *args, **kwargs):
        super(PunishmentForm, self).__init__(*args, **kwargs)

        # Import models here to avoid circular imports
        from models import PunishmentDegree, PunishmentType

        # تحديث خيارات القوائم المنسدلة
        self.degree_id.choices = [(d.id, d.name) for d in PunishmentDegree.query.all()]
        self.type_id.choices = [(t.id, t.name) for t in PunishmentType.query.all()]

class RewardForm(FlaskForm):
    """نموذج إضافة/تعديل المكافآت"""

    type = SelectField('نوع المكافأة', choices=[
        ('مادية', 'مادية'),
        ('معنوية', 'معنوية')
    ], validators=[DataRequired(message='نوع المكافأة مطلوب')])

    reason = TextAreaField('سبب المنح', validators=[
        DataRequired(message='سبب المنح مطلوب'),
        Length(min=10, max=1000, message='سبب المنح يجب أن يكون بين 10 و 1000 حرف')
    ])

    granting_authority = StringField('الهيئة المانحة', validators=[
        DataRequired(message='الهيئة المانحة مطلوبة'),
        Length(min=2, max=200, message='الهيئة المانحة يجب أن تكون بين 2 و 200 حرف')
    ])

    reward_details = StringField('تحديد المكافأة', validators=[
        DataRequired(message='تحديد المكافأة مطلوب'),
        Length(min=2, max=200, message='تحديد المكافأة يجب أن يكون بين 2 و 200 حرف')
    ])

    grant_date = DateField('تاريخ المنح', validators=[
        DataRequired(message='تاريخ المنح مطلوب')
    ])

    submit = SubmitField('حفظ')

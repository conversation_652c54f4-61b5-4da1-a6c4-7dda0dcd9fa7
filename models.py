from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date
from app import db

class Wilaya(db.Model):
    """جدول الولايات"""
    __tablename__ = 'wilayas'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(2), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_fr = db.Column(db.String(100), nullable=True)
    
    # العلاقات
    communes = db.relationship('Commune', backref='wilaya', lazy=True)
    
    def __repr__(self):
        return f'<Wilaya {self.name_ar}>'

class Commune(db.Model):
    """جدول البلديات"""
    __tablename__ = 'communes'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(6), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_fr = db.Column(db.String(100), nullable=True)
    wilaya_id = db.Column(db.Integer, db.ForeignKey('wilayas.id'), nullable=False)
    
    def __repr__(self):
        return f'<Commune {self.name_ar}>'

class Corps(db.Model):
    """جدول الأسلاك"""
    __tablename__ = 'corps'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(50), nullable=False)  # أسلاك خاصة، أسلاك مشتركة، عمال مهنيين
    
    # العلاقات
    ranks = db.relationship('Rank', backref='corps', lazy=True)
    
    def __repr__(self):
        return f'<Corps {self.name}>'

class Rank(db.Model):
    """جدول الرتب"""
    __tablename__ = 'ranks'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    corps_id = db.Column(db.Integer, db.ForeignKey('corps.id'), nullable=False)
    level = db.Column(db.Integer, nullable=False)  # مستوى الرتبة
    
    def __repr__(self):
        return f'<Rank {self.name}>'

class Directorate(db.Model):
    """جدول المديريات"""
    __tablename__ = 'directorates'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    parent_id = db.Column(db.Integer, db.ForeignKey('directorates.id'), nullable=True)
    type = db.Column(db.String(50), nullable=False)  # مديرية، مديرية فرعية، مفتشية، مكتب، فرقة
    
    # العلاقات
    children = db.relationship('Directorate', backref=db.backref('parent', remote_side=[id]))
    services = db.relationship('Service', backref='directorate', lazy=True)
    
    def __repr__(self):
        return f'<Directorate {self.name}>'

class Service(db.Model):
    """جدول المصالح"""
    __tablename__ = 'services'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    directorate_id = db.Column(db.Integer, db.ForeignKey('directorates.id'), nullable=False)
    
    # العلاقات
    positions = db.relationship('Position', backref='service', lazy=True)
    
    def __repr__(self):
        return f'<Service {self.name}>'

class Position(db.Model):
    """جدول الوظائف"""
    __tablename__ = 'positions'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    service_id = db.Column(db.Integer, db.ForeignKey('services.id'), nullable=False)
    
    def __repr__(self):
        return f'<Position {self.name}>'

class Employee(db.Model):
    """جدول الموظفين - البيانات الأساسية"""
    __tablename__ = 'employees'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # البيانات الأساسية
    registration_number = db.Column(db.String(6), unique=True, nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    first_name = db.Column(db.String(100), nullable=False)
    nom_fr = db.Column(db.String(100), nullable=True)
    prenom_fr = db.Column(db.String(100), nullable=True)
    gender = db.Column(db.String(10), nullable=False)  # ذكر، أنثى
    birth_date = db.Column(db.Date, nullable=False)
    birth_wilaya_id = db.Column(db.Integer, db.ForeignKey('wilayas.id'), nullable=False)
    birth_commune_id = db.Column(db.Integer, db.ForeignKey('communes.id'), nullable=False)
    marital_status = db.Column(db.String(20), nullable=False)  # أعزب، متزوج، مطلق، أرمل
    children_count = db.Column(db.Integer, default=0)
    dependents_count = db.Column(db.Integer, default=0)
    blood_type = db.Column(db.String(5), nullable=True)
    photo = db.Column(db.String(255), nullable=True)
    
    # بيانات الاتصال
    phone1 = db.Column(db.String(20), nullable=True)
    phone2 = db.Column(db.String(20), nullable=True)
    email = db.Column(db.String(120), nullable=True)
    primary_address = db.Column(db.Text, nullable=True)
    secondary_address = db.Column(db.Text, nullable=True)
    emergency_contact_name = db.Column(db.String(200), nullable=True)
    emergency_contact_address = db.Column(db.Text, nullable=True)
    
    # البيانات المهنية
    status = db.Column(db.String(20), nullable=False, default='نشط')  # نشط، تحويل، موقف، استيداع، منتدب، متوفي، مفصول، مستقيل
    corps_id = db.Column(db.Integer, db.ForeignKey('corps.id'), nullable=False)
    current_rank_id = db.Column(db.Integer, db.ForeignKey('ranks.id'), nullable=False)
    rank_promotion_date = db.Column(db.Date, nullable=True)
    current_position_id = db.Column(db.Integer, db.ForeignKey('positions.id'), nullable=False)
    position_assignment_date = db.Column(db.Date, nullable=True)
    directorate_id = db.Column(db.Integer, db.ForeignKey('directorates.id'), nullable=False)
    service_id = db.Column(db.Integer, db.ForeignKey('services.id'), nullable=False)
    assignment_location = db.Column(db.String(200), nullable=True)
    hiring_date = db.Column(db.Date, nullable=False)
    hiring_rank_id = db.Column(db.Integer, db.ForeignKey('ranks.id'), nullable=False)
    
    # الوثائق والأرقام
    social_security_number = db.Column(db.String(15), unique=True, nullable=False)
    postal_account_number = db.Column(db.String(10), nullable=True)
    professional_card_number = db.Column(db.String(50), nullable=True)
    professional_card_issue_date = db.Column(db.Date, nullable=True)
    national_id_number = db.Column(db.String(18), unique=True, nullable=False)
    national_id_issue_date = db.Column(db.Date, nullable=True)
    national_id_issue_place_id = db.Column(db.Integer, db.ForeignKey('communes.id'), nullable=True)
    driving_license_number = db.Column(db.String(50), nullable=True)
    driving_license_category = db.Column(db.String(10), nullable=True)
    driving_license_issue_date = db.Column(db.Date, nullable=True)
    driving_license_issue_place_id = db.Column(db.Integer, db.ForeignKey('communes.id'), nullable=True)
    mutual_card_number = db.Column(db.String(50), nullable=True)
    mutual_card_issue_date = db.Column(db.Date, nullable=True)
    practiced_sport = db.Column(db.String(100), nullable=True)
    
    # العلاقات
    birth_wilaya = db.relationship('Wilaya', foreign_keys=[birth_wilaya_id])
    birth_commune = db.relationship('Commune', foreign_keys=[birth_commune_id])
    corps = db.relationship('Corps')
    current_rank = db.relationship('Rank', foreign_keys=[current_rank_id])
    hiring_rank = db.relationship('Rank', foreign_keys=[hiring_rank_id])
    current_position = db.relationship('Position')
    directorate = db.relationship('Directorate')
    service = db.relationship('Service')
    national_id_issue_place = db.relationship('Commune', foreign_keys=[national_id_issue_place_id])
    driving_license_issue_place = db.relationship('Commune', foreign_keys=[driving_license_issue_place_id])
    
    # العلاقات مع الجداول الفرعية
    certificates = db.relationship('Certificate', backref='employee', lazy=True, cascade='all, delete-orphan')
    trainings = db.relationship('Training', backref='employee', lazy=True, cascade='all, delete-orphan')
    languages = db.relationship('EmployeeLanguage', backref='employee', lazy=True, cascade='all, delete-orphan')
    transfers = db.relationship('Transfer', backref='employee', lazy=True, cascade='all, delete-orphan')
    annual_leaves = db.relationship('AnnualLeave', backref='employee', lazy=True, cascade='all, delete-orphan')
    sick_leaves = db.relationship('SickLeave', backref='employee', lazy=True, cascade='all, delete-orphan')
    other_leaves = db.relationship('OtherLeave', backref='employee', lazy=True, cascade='all, delete-orphan')
    
    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Employee {self.registration_number}: {self.first_name} {self.last_name}>'
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def age(self):
        today = date.today()
        return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))

class Certificate(db.Model):
    """جدول الشهادات"""
    __tablename__ = 'certificates'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    certificate_type = db.Column(db.String(100), nullable=False)  # دكتوراه، ماستر، ليسانس، تقني سامي
    specialization = db.Column(db.String(200), nullable=False)
    year_obtained = db.Column(db.Integer, nullable=False)
    institution = db.Column(db.String(200), nullable=False)

    def __repr__(self):
        return f'<Certificate {self.certificate_type}: {self.specialization}>'

class Training(db.Model):
    """جدول التكوين"""
    __tablename__ = 'trainings'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    subject = db.Column(db.String(200), nullable=False)
    duration = db.Column(db.String(100), nullable=False)  # المدة الزمنية
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)

    def __repr__(self):
        return f'<Training {self.subject}>'

class Language(db.Model):
    """جدول اللغات"""
    __tablename__ = 'languages'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)

    def __repr__(self):
        return f'<Language {self.name}>'

class EmployeeLanguage(db.Model):
    """جدول لغات الموظف"""
    __tablename__ = 'employee_languages'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    language_id = db.Column(db.Integer, db.ForeignKey('languages.id'), nullable=False)
    can_write = db.Column(db.Boolean, default=False)
    writing_level = db.Column(db.String(20), nullable=True)  # ممتاز، جيد، متوسط
    can_read = db.Column(db.Boolean, default=False)
    reading_level = db.Column(db.String(20), nullable=True)  # ممتاز، جيد، متوسط

    # العلاقات
    language = db.relationship('Language')

    def __repr__(self):
        return f'<EmployeeLanguage {self.language.name}>'

class Transfer(db.Model):
    """جدول التحويلات والتنقلات"""
    __tablename__ = 'transfers'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    directorate_id = db.Column(db.Integer, db.ForeignKey('directorates.id'), nullable=False)
    service_id = db.Column(db.Integer, db.ForeignKey('services.id'), nullable=False)
    assignment_location = db.Column(db.String(200), nullable=False)
    installation_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=True)
    duration_days = db.Column(db.Integer, nullable=True)  # محسوبة
    position_id = db.Column(db.Integer, db.ForeignKey('positions.id'), nullable=False)
    decision_number = db.Column(db.String(100), nullable=True)
    decision_date = db.Column(db.Date, nullable=True)

    # العلاقات
    directorate = db.relationship('Directorate')
    service = db.relationship('Service')
    position = db.relationship('Position')

    def __repr__(self):
        return f'<Transfer {self.assignment_location}>'

class AnnualLeave(db.Model):
    """جدول العطل السنوية"""
    __tablename__ = 'annual_leaves'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    year = db.Column(db.Integer, nullable=False)
    days_count = db.Column(db.Integer, nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)  # محسوب
    remaining_days = db.Column(db.Integer, nullable=False)  # محسوب
    destination = db.Column(db.String(200), nullable=True)
    decision_number = db.Column(db.String(100), nullable=True)
    decision_date = db.Column(db.Date, nullable=True)

    def __repr__(self):
        return f'<AnnualLeave {self.year}: {self.days_count} days>'

class SickLeave(db.Model):
    """جدول العطل المرضية"""
    __tablename__ = 'sick_leaves'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    leave_type = db.Column(db.String(100), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    days_count = db.Column(db.Integer, nullable=False)
    end_date = db.Column(db.Date, nullable=False)  # محسوب
    is_indexed = db.Column(db.Boolean, default=False)
    internal_medical_control = db.Column(db.Boolean, default=False)
    doctor_opinion = db.Column(db.String(20), nullable=True)  # مقبولة، غير مقبولة
    deduction_number = db.Column(db.String(100), nullable=True)
    deduction_date = db.Column(db.Date, nullable=True)

    def __repr__(self):
        return f'<SickLeave {self.leave_type}: {self.days_count} days>'

class OtherLeave(db.Model):
    """جدول العطل الأخرى"""
    __tablename__ = 'other_leaves'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    leave_type = db.Column(db.String(100), nullable=False)  # استثنائية
    start_date = db.Column(db.Date, nullable=False)
    days_count = db.Column(db.Integer, nullable=False)
    end_date = db.Column(db.Date, nullable=False)  # محسوب
    reason = db.Column(db.Text, nullable=False)
    destination = db.Column(db.String(200), nullable=True)
    decision_number = db.Column(db.String(100), nullable=True)
    decision_date = db.Column(db.Date, nullable=True)
    salary_deduction = db.Column(db.Boolean, default=False)
    deduction_number = db.Column(db.String(100), nullable=True)
    deduction_date = db.Column(db.Date, nullable=True)

    def __repr__(self):
        return f'<OtherLeave {self.leave_type}: {self.days_count} days>'

class Deposit(db.Model):
    """جدول الاستيداع"""
    __tablename__ = 'deposits'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    period = db.Column(db.String(20), nullable=False)  # الأولى، الثانية
    duration_years = db.Column(db.Integer, nullable=False)
    reason = db.Column(db.String(200), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)  # محسوب
    decision_number = db.Column(db.String(100), nullable=True)
    decision_date = db.Column(db.Date, nullable=True)

    def __repr__(self):
        return f'<Deposit {self.period}: {self.duration_years} years>'

class Suspension(db.Model):
    """جدول التوقيف"""
    __tablename__ = 'suspensions'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    suspension_date = db.Column(db.Date, nullable=False)
    reason = db.Column(db.Text, nullable=False)
    decision_number = db.Column(db.String(100), nullable=True)
    decision_date = db.Column(db.Date, nullable=True)

    def __repr__(self):
        return f'<Suspension {self.suspension_date}>'

class Death(db.Model):
    """جدول الوفيات"""
    __tablename__ = 'deaths'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    death_date = db.Column(db.Date, nullable=False)
    cause = db.Column(db.String(50), nullable=False)  # عادية، حادث عمل
    certificate_number = db.Column(db.String(100), nullable=True)

    def __repr__(self):
        return f'<Death {self.death_date}: {self.cause}>'

class Delegation(db.Model):
    """جدول الانتداب"""
    __tablename__ = 'delegations'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    delegation_date = db.Column(db.Date, nullable=False)
    duration = db.Column(db.String(100), nullable=False)
    reason = db.Column(db.Text, nullable=False)
    location = db.Column(db.String(200), nullable=False)

    def __repr__(self):
        return f'<Delegation {self.location}>'

class Resignation(db.Model):
    """جدول الاستقالة"""
    __tablename__ = 'resignations'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    request_date = db.Column(db.Date, nullable=False)
    reason = db.Column(db.Text, nullable=False)
    is_accepted = db.Column(db.Boolean, nullable=True)
    decision_number = db.Column(db.String(100), nullable=True)
    decision_date = db.Column(db.Date, nullable=True)

    def __repr__(self):
        return f'<Resignation {self.request_date}>'

class ExternalTransfer(db.Model):
    """جدول التحويل الخارجي"""
    __tablename__ = 'external_transfers'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    destination_directorate = db.Column(db.String(200), nullable=False)
    decision_number = db.Column(db.String(100), nullable=True)
    decision_date = db.Column(db.Date, nullable=True)

    def __repr__(self):
        return f'<ExternalTransfer {self.destination_directorate}>'

class RankPromotion(db.Model):
    """جدول الترقية في الرتبة"""
    __tablename__ = 'rank_promotions'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    old_rank_id = db.Column(db.Integer, db.ForeignKey('ranks.id'), nullable=False)
    new_rank_id = db.Column(db.Integer, db.ForeignKey('ranks.id'), nullable=False)
    promotion_type = db.Column(db.String(100), nullable=False)
    decision_number = db.Column(db.String(100), nullable=True)
    decision_date = db.Column(db.Date, nullable=False)

    # العلاقات
    old_rank = db.relationship('Rank', foreign_keys=[old_rank_id])
    new_rank = db.relationship('Rank', foreign_keys=[new_rank_id])

    def __repr__(self):
        return f'<RankPromotion {self.old_rank.name} -> {self.new_rank.name}>'

class GradePromotion(db.Model):
    """جدول الترقية في الدرجة"""
    __tablename__ = 'grade_promotions'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    grade = db.Column(db.Integer, nullable=False)  # من 1 إلى 12
    decision_number = db.Column(db.String(100), nullable=True)
    decision_date = db.Column(db.Date, nullable=False)

    def __repr__(self):
        return f'<GradePromotion Grade {self.grade}>'

class Spouse(db.Model):
    """جدول الزوجة"""
    __tablename__ = 'spouses'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    birth_date = db.Column(db.Date, nullable=False)
    birth_wilaya_id = db.Column(db.Integer, db.ForeignKey('wilayas.id'), nullable=False)
    birth_commune_id = db.Column(db.Integer, db.ForeignKey('communes.id'), nullable=False)
    job = db.Column(db.String(200), nullable=True)
    workplace = db.Column(db.String(200), nullable=True)

    # العلاقات
    birth_wilaya = db.relationship('Wilaya', foreign_keys=[birth_wilaya_id])
    birth_commune = db.relationship('Commune', foreign_keys=[birth_commune_id])

    def __repr__(self):
        return f'<Spouse {self.first_name} {self.last_name}>'

class Child(db.Model):
    """جدول الأولاد"""
    __tablename__ = 'children'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    first_name = db.Column(db.String(100), nullable=False)
    gender = db.Column(db.String(10), nullable=False)
    is_student = db.Column(db.Boolean, default=False)
    education_level = db.Column(db.String(100), nullable=True)
    school_institution = db.Column(db.String(200), nullable=True)

    def __repr__(self):
        return f'<Child {self.first_name} {self.last_name}>'

class Dependent(db.Model):
    """جدول الأشخاص المتكفل بهم"""
    __tablename__ = 'dependents'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    first_name = db.Column(db.String(100), nullable=False)
    gender = db.Column(db.String(10), nullable=False)
    is_student = db.Column(db.Boolean, default=False)
    education_level = db.Column(db.String(100), nullable=True)
    school_institution = db.Column(db.String(200), nullable=True)

    def __repr__(self):
        return f'<Dependent {self.first_name} {self.last_name}>'

class PunishmentDegree(db.Model):
    """جدول درجات العقوبات"""
    __tablename__ = 'punishment_degrees'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    level = db.Column(db.Integer, nullable=False)

    def __repr__(self):
        return f'<PunishmentDegree {self.name}>'

class PunishmentType(db.Model):
    """جدول أنواع العقوبات"""
    __tablename__ = 'punishment_types'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    degree_id = db.Column(db.Integer, db.ForeignKey('punishment_degrees.id'), nullable=False)

    # العلاقات
    degree = db.relationship('PunishmentDegree')

    def __repr__(self):
        return f'<PunishmentType {self.name}>'

class Punishment(db.Model):
    """جدول العقوبات"""
    __tablename__ = 'punishments'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    degree_id = db.Column(db.Integer, db.ForeignKey('punishment_degrees.id'), nullable=False)
    type_id = db.Column(db.Integer, db.ForeignKey('punishment_types.id'), nullable=False)
    reason = db.Column(db.Text, nullable=False)
    decision_number = db.Column(db.String(100), nullable=True)
    decision_date = db.Column(db.Date, nullable=False)

    # العلاقات
    degree = db.relationship('PunishmentDegree')
    type = db.relationship('PunishmentType')

    def __repr__(self):
        return f'<Punishment {self.type.name}>'

class Reward(db.Model):
    """جدول المكافآت"""
    __tablename__ = 'rewards'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    type = db.Column(db.String(20), nullable=False)  # مادية، معنوية
    reason = db.Column(db.Text, nullable=False)
    granting_authority = db.Column(db.String(200), nullable=False)
    reward_details = db.Column(db.String(200), nullable=False)  # شهادة، وسام، إلخ
    grant_date = db.Column(db.Date, nullable=False)

    def __repr__(self):
        return f'<Reward {self.type}: {self.reward_details}>'

class Institution(db.Model):
    """جدول بيانات المؤسسة"""
    __tablename__ = 'institutions'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    fax = db.Column(db.String(20), nullable=True)
    email = db.Column(db.String(120), nullable=True)
    website = db.Column(db.String(200), nullable=True)
    address = db.Column(db.Text, nullable=True)
    logo = db.Column(db.String(255), nullable=True)

    def __repr__(self):
        return f'<Institution {self.name}>'

def init_default_data():
    """تهيئة البيانات الافتراضية"""
    # إضافة الولايات الجزائرية
    if Wilaya.query.count() == 0:
        wilayas_data = [
            ('01', 'أدرار', 'Adrar'),
            ('02', 'الشلف', 'Chlef'),
            ('03', 'الأغواط', 'Laghouat'),
            ('04', 'أم البواقي', 'Oum El Bouaghi'),
            ('05', 'باتنة', 'Batna'),
            ('06', 'بجاية', 'Béjaïa'),
            ('07', 'بسكرة', 'Biskra'),
            ('08', 'بشار', 'Béchar'),
            ('09', 'البليدة', 'Blida'),
            ('10', 'البويرة', 'Bouira'),
            ('11', 'تمنراست', 'Tamanrasset'),
            ('12', 'تبسة', 'Tébessa'),
            ('13', 'تلمسان', 'Tlemcen'),
            ('14', 'تيارت', 'Tiaret'),
            ('15', 'تيزي وزو', 'Tizi Ouzou'),
            ('16', 'الجزائر', 'Alger'),
            ('17', 'الجلفة', 'Djelfa'),
            ('18', 'جيجل', 'Jijel'),
            ('19', 'سطيف', 'Sétif'),
            ('20', 'سعيدة', 'Saïda'),
            ('21', 'سكيكدة', 'Skikda'),
            ('22', 'سيدي بلعباس', 'Sidi Bel Abbès'),
            ('23', 'عنابة', 'Annaba'),
            ('24', 'قالمة', 'Guelma'),
            ('25', 'قسنطينة', 'Constantine'),
            ('26', 'المدية', 'Médéa'),
            ('27', 'مستغانم', 'Mostaganem'),
            ('28', 'المسيلة', 'M\'Sila'),
            ('29', 'معسكر', 'Mascara'),
            ('30', 'ورقلة', 'Ouargla'),
            ('31', 'وهران', 'Oran'),
            ('32', 'البيض', 'El Bayadh'),
            ('33', 'إليزي', 'Illizi'),
            ('34', 'برج بوعريريج', 'Bordj Bou Arréridj'),
            ('35', 'بومرداس', 'Boumerdès'),
            ('36', 'الطارف', 'El Tarf'),
            ('37', 'تندوف', 'Tindouf'),
            ('38', 'تيسمسيلت', 'Tissemsilt'),
            ('39', 'الوادي', 'El Oued'),
            ('40', 'خنشلة', 'Khenchela'),
            ('41', 'سوق أهراس', 'Souk Ahras'),
            ('42', 'تيبازة', 'Tipaza'),
            ('43', 'ميلة', 'Mila'),
            ('44', 'عين الدفلى', 'Aïn Defla'),
            ('45', 'النعامة', 'Naâma'),
            ('46', 'عين تموشنت', 'Aïn Témouchent'),
            ('47', 'غرداية', 'Ghardaïa'),
            ('48', 'غليزان', 'Relizane'),
            ('49', 'تيميمون', 'Timimoun'),
            ('50', 'برج باجي مختار', 'Bordj Badji Mokhtar'),
            ('51', 'أولاد جلال', 'Ouled Djellal'),
            ('52', 'بني عباس', 'Béni Abbès'),
            ('53', 'عين صالح', 'In Salah'),
            ('54', 'عين قزام', 'In Guezzam'),
            ('55', 'تقرت', 'Touggourt'),
            ('56', 'جانت', 'Djanet'),
            ('57', 'المغير', 'El M\'Ghair'),
            ('58', 'المنيعة', 'El Meniaa')
        ]

        for code, name_ar, name_fr in wilayas_data:
            wilaya = Wilaya(code=code, name_ar=name_ar, name_fr=name_fr)
            db.session.add(wilaya)

        db.session.commit()

    # إضافة الأسلاك الافتراضية
    if Corps.query.count() == 0:
        corps_data = [
            ('أسلاك خاصة', 'خاص'),
            ('أسلاك مشتركة', 'مشترك'),
            ('عمال مهنيين', 'مهني')
        ]

        for name, type_name in corps_data:
            corps = Corps(name=name, type=type_name)
            db.session.add(corps)

        db.session.commit()

    # إضافة اللغات الافتراضية
    if Language.query.count() == 0:
        languages = ['العربية', 'الفرنسية', 'الإنجليزية', 'الأمازيغية', 'الألمانية', 'الإسبانية', 'الإيطالية']

        for lang_name in languages:
            language = Language(name=lang_name)
            db.session.add(language)

        db.session.commit()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تهيئة قاعدة البيانات مع البيانات الأساسية
"""

from app import app, db
from models import *

def init_database():
    """تهيئة قاعدة البيانات مع البيانات الأساسية"""
    
    with app.app_context():
        # إنشاء جميع الجداول
        db.create_all()
        
        print("تم إنشاء جداول قاعدة البيانات بنجاح...")
        
        # تهيئة الولايات الجزائرية
        init_wilayas()
        
        # تهيئة بعض البلديات كمثال
        init_sample_communes()
        
        # تهيئة الأسلاك
        init_corps()
        
        # تهيئة الرتب
        init_ranks()
        
        # تهيئة المديريات
        init_directorates()
        
        # تهيئة المصالح
        init_services()
        
        # تهيئة الوظائف
        init_positions()
        
        # تهيئة اللغات
        init_languages()
        
        # تهيئة درجات وأنواع العقوبات
        init_punishments()
        
        # تهيئة بيانات المؤسسة
        init_institution()
        
        print("تم تهيئة جميع البيانات الأساسية بنجاح!")

def init_wilayas():
    """تهيئة الولايات الجزائرية"""
    if Wilaya.query.count() > 0:
        print("الولايات موجودة مسبقاً...")
        return
    
    wilayas_data = [
        ('01', 'أدرار', 'Adrar'),
        ('02', 'الشلف', 'Chlef'),
        ('03', 'الأغواط', 'Laghouat'),
        ('04', 'أم البواقي', 'Oum El Bouaghi'),
        ('05', 'باتنة', 'Batna'),
        ('06', 'بجاية', 'Béjaïa'),
        ('07', 'بسكرة', 'Biskra'),
        ('08', 'بشار', 'Béchar'),
        ('09', 'البليدة', 'Blida'),
        ('10', 'البويرة', 'Bouira'),
        ('11', 'تمنراست', 'Tamanrasset'),
        ('12', 'تبسة', 'Tébessa'),
        ('13', 'تلمسان', 'Tlemcen'),
        ('14', 'تيارت', 'Tiaret'),
        ('15', 'تيزي وزو', 'Tizi Ouzou'),
        ('16', 'الجزائر', 'Alger'),
        ('17', 'الجلفة', 'Djelfa'),
        ('18', 'جيجل', 'Jijel'),
        ('19', 'سطيف', 'Sétif'),
        ('20', 'سعيدة', 'Saïda'),
        ('21', 'سكيكدة', 'Skikda'),
        ('22', 'سيدي بلعباس', 'Sidi Bel Abbès'),
        ('23', 'عنابة', 'Annaba'),
        ('24', 'قالمة', 'Guelma'),
        ('25', 'قسنطينة', 'Constantine'),
        ('26', 'المدية', 'Médéa'),
        ('27', 'مستغانم', 'Mostaganem'),
        ('28', 'المسيلة', 'M\'Sila'),
        ('29', 'معسكر', 'Mascara'),
        ('30', 'ورقلة', 'Ouargla'),
        ('31', 'وهران', 'Oran'),
        ('32', 'البيض', 'El Bayadh'),
        ('33', 'إليزي', 'Illizi'),
        ('34', 'برج بوعريريج', 'Bordj Bou Arréridj'),
        ('35', 'بومرداس', 'Boumerdès'),
        ('36', 'الطارف', 'El Tarf'),
        ('37', 'تندوف', 'Tindouf'),
        ('38', 'تيسمسيلت', 'Tissemsilt'),
        ('39', 'الوادي', 'El Oued'),
        ('40', 'خنشلة', 'Khenchela'),
        ('41', 'سوق أهراس', 'Souk Ahras'),
        ('42', 'تيبازة', 'Tipaza'),
        ('43', 'ميلة', 'Mila'),
        ('44', 'عين الدفلى', 'Aïn Defla'),
        ('45', 'النعامة', 'Naâma'),
        ('46', 'عين تموشنت', 'Aïn Témouchent'),
        ('47', 'غرداية', 'Ghardaïa'),
        ('48', 'غليزان', 'Relizane'),
        ('49', 'تيميمون', 'Timimoun'),
        ('50', 'برج باجي مختار', 'Bordj Badji Mokhtar'),
        ('51', 'أولاد جلال', 'Ouled Djellal'),
        ('52', 'بني عباس', 'Béni Abbès'),
        ('53', 'عين صالح', 'In Salah'),
        ('54', 'عين قزام', 'In Guezzam'),
        ('55', 'تقرت', 'Touggourt'),
        ('56', 'جانت', 'Djanet'),
        ('57', 'المغير', 'El M\'Ghair'),
        ('58', 'المنيعة', 'El Meniaa')
    ]
    
    for code, name_ar, name_fr in wilayas_data:
        wilaya = Wilaya(code=code, name_ar=name_ar, name_fr=name_fr)
        db.session.add(wilaya)
    
    db.session.commit()
    print(f"تم إضافة {len(wilayas_data)} ولاية...")

def init_sample_communes():
    """تهيئة بعض البلديات كمثال"""
    if Commune.query.count() > 0:
        print("البلديات موجودة مسبقاً...")
        return
    
    # بلديات ولاية الأغواط كمثال
    laghouat_wilaya = Wilaya.query.filter_by(code='03').first()
    if laghouat_wilaya:
        communes_data = [
            ('030001', 'الأغواط', 'Laghouat'),
            ('030002', 'كاف الأحجار', 'Kef El Ahjar'),
            ('030003', 'قلتة سيدي سعد', 'Gueltet Sidi Saad'),
            ('030004', 'عين ماضي', 'Aïn Madhi'),
            ('030005', 'تاجموت', 'Tadjemout'),
            ('030006', 'خنق ميدن', 'Kheneg Miden'),
            ('030007', 'قصر الحيران', 'Ksar El Hirane'),
            ('030008', 'سيدي مخلوف', 'Sidi Makhlouf'),
            ('030009', 'حاسي الدلاعة', 'Hassi Delaa'),
            ('030010', 'حاسي رمل', 'Hassi R\'Mel')
        ]
        
        for code, name_ar, name_fr in communes_data:
            commune = Commune(code=code, name_ar=name_ar, name_fr=name_fr, wilaya_id=laghouat_wilaya.id)
            db.session.add(commune)
        
        db.session.commit()
        print(f"تم إضافة {len(communes_data)} بلدية لولاية الأغواط...")

def init_corps():
    """تهيئة الأسلاك"""
    if Corps.query.count() > 0:
        print("الأسلاك موجودة مسبقاً...")
        return
    
    corps_data = [
        ('أسلاك خاصة بإدارة الجمارك', 'خاص'),
        ('أسلاك مشتركة', 'مشترك'),
        ('عمال مهنيين', 'مهني')
    ]
    
    for name, type_name in corps_data:
        corps = Corps(name=name, type=type_name)
        db.session.add(corps)
    
    db.session.commit()
    print(f"تم إضافة {len(corps_data)} سلك...")

def init_ranks():
    """تهيئة الرتب"""
    if Rank.query.count() > 0:
        print("الرتب موجودة مسبقاً...")
        return
    
    # الحصول على الأسلاك
    corps_khass = Corps.query.filter_by(type='خاص').first()
    corps_mushtarak = Corps.query.filter_by(type='مشترك').first()
    corps_mihani = Corps.query.filter_by(type='مهني').first()
    
    ranks_data = []
    
    # رتب الأسلاك الخاصة
    if corps_khass:
        ranks_data.extend([
            ('مدير عام للجمارك', corps_khass.id, 15),
            ('مدير جهوي للجمارك', corps_khass.id, 14),
            ('مدير فرعي للجمارك', corps_khass.id, 13),
            ('مفتش رئيسي للجمارك', corps_khass.id, 12),
            ('مفتش للجمارك', corps_khass.id, 11),
            ('مفتش مساعد للجمارك', corps_khass.id, 10),
            ('ضابط جمارك رئيسي', corps_khass.id, 9),
            ('ضابط جمارك', corps_khass.id, 8),
            ('عون تحكم في الجمارك', corps_khass.id, 7),
            ('عون جمارك', corps_khass.id, 6)
        ])
    
    # رتب الأسلاك المشتركة
    if corps_mushtarak:
        ranks_data.extend([
            ('مدير إدارة مركزية', corps_mushtarak.id, 15),
            ('مدير دراسات', corps_mushtarak.id, 14),
            ('مدير دراسات مساعد', corps_mushtarak.id, 13),
            ('محافظ محاسب رئيسي', corps_mushtarak.id, 12),
            ('محافظ محاسب', corps_mushtarak.id, 11),
            ('محاسب إداري رئيسي', corps_mushtarak.id, 10),
            ('محاسب إداري', corps_mushtarak.id, 9),
            ('كاتب مديرية', corps_mushtarak.id, 8),
            ('كاتب', corps_mushtarak.id, 7)
        ])
    
    # رتب العمال المهنيين
    if corps_mihani:
        ranks_data.extend([
            ('عامل مهني من المستوى الثالث', corps_mihani.id, 6),
            ('عامل مهني من المستوى الثاني', corps_mihani.id, 5),
            ('عامل مهني من المستوى الأول', corps_mihani.id, 4)
        ])
    
    for name, corps_id, level in ranks_data:
        rank = Rank(name=name, corps_id=corps_id, level=level)
        db.session.add(rank)
    
    db.session.commit()
    print(f"تم إضافة {len(ranks_data)} رتبة...")

def init_directorates():
    """تهيئة المديريات"""
    if Directorate.query.count() > 0:
        print("المديريات موجودة مسبقاً...")
        return
    
    # المديرية الجهوية الرئيسية
    main_directorate = Directorate(
        name='المديرية الجهوية للجمارك بالأغواط',
        type='مديرية جهوية'
    )
    db.session.add(main_directorate)
    db.session.commit()
    
    # المديريات الفرعية
    sub_directorates_data = [
        ('المديرية الفرعية لإدارة الوسائل', 'مديرية فرعية'),
        ('المديرية الفرعية للمنازعات الجمركية والتحصيل', 'مديرية فرعية'),
        ('المديرية الفرعية للإعلام الآلي والاتصال', 'مديرية فرعية')
    ]
    
    for name, type_name in sub_directorates_data:
        directorate = Directorate(
            name=name,
            type=type_name,
            parent_id=main_directorate.id
        )
        db.session.add(directorate)
    
    # مفتشيات
    inspectorates_data = [
        ('مفتشية أقسام الجمارك بالأغواط', 'مفتشية'),
        ('المفتشية الرئيسية للفرق', 'مفتشية رئيسية'),
        ('المفتشية الرئيسية للعمليات التجارية', 'مفتشية رئيسية'),
        ('المفتشية الرئيسية للمحروقات', 'مفتشية رئيسية')
    ]
    
    for name, type_name in inspectorates_data:
        directorate = Directorate(
            name=name,
            type=type_name,
            parent_id=main_directorate.id
        )
        db.session.add(directorate)
    
    db.session.commit()
    print("تم إضافة المديريات والمفتشيات...")

def init_services():
    """تهيئة المصالح"""
    if Service.query.count() > 0:
        print("المصالح موجودة مسبقاً...")
        return
    
    # الحصول على المديريات
    admin_directorate = Directorate.query.filter_by(name='المديرية الفرعية لإدارة الوسائل').first()
    it_directorate = Directorate.query.filter_by(name='المديرية الفرعية للإعلام الآلي والاتصال').first()
    
    services_data = []
    
    if admin_directorate:
        services_data.extend([
            ('مكتب تسيير المستخدمين والتكوين', admin_directorate.id),
            ('مكتب الوسائل الإمدادية', admin_directorate.id),
            ('مكتب الميزانية والمحاسبة', admin_directorate.id)
        ])
    
    if it_directorate:
        services_data.extend([
            ('مكتب الاتصال', it_directorate.id),
            ('مكتب الإعلام الآلي', it_directorate.id),
            ('مكتب النجاعة والإحصائيات', it_directorate.id)
        ])
    
    for name, directorate_id in services_data:
        service = Service(name=name, directorate_id=directorate_id)
        db.session.add(service)
    
    db.session.commit()
    print(f"تم إضافة {len(services_data)} مصلحة...")

def init_positions():
    """تهيئة الوظائف"""
    if Position.query.count() > 0:
        print("الوظائف موجودة مسبقاً...")
        return
    
    # الحصول على المصالح
    hr_service = Service.query.filter_by(name='مكتب تسيير المستخدمين والتكوين').first()
    it_service = Service.query.filter_by(name='مكتب الإعلام الآلي').first()
    
    positions_data = []
    
    if hr_service:
        positions_data.extend([
            ('رئيس مكتب تسيير المستخدمين والتكوين', hr_service.id),
            ('مكلف بالموارد البشرية', hr_service.id),
            ('مكلف بالتكوين', hr_service.id),
            ('كاتب', hr_service.id)
        ])
    
    if it_service:
        positions_data.extend([
            ('رئيس مكتب الإعلام الآلي', it_service.id),
            ('مهندس إعلام آلي', it_service.id),
            ('تقني إعلام آلي', it_service.id),
            ('مكلف بالصيانة', it_service.id)
        ])
    
    for name, service_id in positions_data:
        position = Position(name=name, service_id=service_id)
        db.session.add(position)
    
    db.session.commit()
    print(f"تم إضافة {len(positions_data)} وظيفة...")

def init_languages():
    """تهيئة اللغات"""
    if Language.query.count() > 0:
        print("اللغات موجودة مسبقاً...")
        return
    
    languages = ['العربية', 'الفرنسية', 'الإنجليزية', 'الأمازيغية', 'الألمانية', 'الإسبانية', 'الإيطالية']
    
    for lang_name in languages:
        language = Language(name=lang_name)
        db.session.add(language)
    
    db.session.commit()
    print(f"تم إضافة {len(languages)} لغة...")

def init_punishments():
    """تهيئة درجات وأنواع العقوبات"""
    if PunishmentDegree.query.count() > 0:
        print("العقوبات موجودة مسبقاً...")
        return
    
    # درجات العقوبات
    degrees_data = [
        ('الدرجة الأولى', 1),
        ('الدرجة الثانية', 2),
        ('الدرجة الثالثة', 3),
        ('الدرجة الرابعة', 4)
    ]
    
    for name, level in degrees_data:
        degree = PunishmentDegree(name=name, level=level)
        db.session.add(degree)
    
    db.session.commit()
    
    # أنواع العقوبات
    degree1 = PunishmentDegree.query.filter_by(level=1).first()
    degree2 = PunishmentDegree.query.filter_by(level=2).first()
    degree3 = PunishmentDegree.query.filter_by(level=3).first()
    degree4 = PunishmentDegree.query.filter_by(level=4).first()
    
    types_data = []
    
    if degree1:
        types_data.extend([
            ('التنبيه', degree1.id),
            ('الإنذار', degree1.id)
        ])
    
    if degree2:
        types_data.extend([
            ('التوبيخ', degree2.id),
            ('الحسم من الراتب', degree2.id)
        ])
    
    if degree3:
        types_data.extend([
            ('التوقيف المؤقت', degree3.id),
            ('التنزيل في الرتبة', degree3.id)
        ])
    
    if degree4:
        types_data.extend([
            ('الفصل من الخدمة', degree4.id),
            ('الإقالة', degree4.id)
        ])
    
    for name, degree_id in types_data:
        punishment_type = PunishmentType(name=name, degree_id=degree_id)
        db.session.add(punishment_type)
    
    db.session.commit()
    print(f"تم إضافة {len(degrees_data)} درجة عقوبة و {len(types_data)} نوع عقوبة...")

def init_institution():
    """تهيئة بيانات المؤسسة"""
    if Institution.query.count() > 0:
        print("بيانات المؤسسة موجودة مسبقاً...")
        return
    
    institution = Institution(
        name='المديرية الجهوية للجمارك بالأغواط',
        phone='027-93-XX-XX',
        fax='027-93-XX-XX',
        email='<EMAIL>',
        website='www.douane.gov.dz',
        address='شارع الاستقلال، الأغواط 03000، الجزائر'
    )
    
    db.session.add(institution)
    db.session.commit()
    print("تم إضافة بيانات المؤسسة...")

if __name__ == '__main__':
    init_database()

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_wtf import <PERSON>laskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import StringField, IntegerField, SelectField, DateField, TextAreaField, BooleanField
from wtforms.validators import DataRequired, Length, Email, Optional, NumberRange
from werkzeug.utils import secure_filename
from datetime import datetime, date
import os
import sqlite3
from PIL import Image
import re
from config import get_config, CUSTOMS_CONFIG

app = Flask(__name__)
app.config.from_object(get_config())

db = SQLAlchemy(app)

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Import models will be done after app context

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    from models import Employee

    total_employees = Employee.query.count()
    active_employees = Employee.query.filter_by(status='نشط').count()
    return render_template('index.html',
                         total_employees=total_employees,
                         active_employees=active_employees)

@app.route('/employees')
def employees_list():
    """قائمة الموظفين"""
    from models import Employee

    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)

    query = Employee.query
    if search:
        query = query.filter(
            db.or_(
                Employee.first_name.contains(search),
                Employee.last_name.contains(search),
                Employee.registration_number.contains(search)
            )
        )

    employees = query.paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('employees/list.html', employees=employees, search=search)

@app.route('/employee/add', methods=['GET', 'POST'])
def add_employee():
    """إضافة موظف جديد"""
    from models import Employee
    from forms import EmployeeForm

    form = EmployeeForm()

    if form.validate_on_submit():
        employee = Employee()
        form.populate_obj(employee)

        # Handle photo upload
        if form.photo.data:
            photo_filename = save_employee_photo(form.photo.data, employee.registration_number)
            employee.photo = photo_filename

        db.session.add(employee)
        db.session.commit()
        flash('تم إضافة الموظف بنجاح', 'success')
        return redirect(url_for('employee_detail', id=employee.id))

    return render_template('employees/add.html', form=form)

@app.route('/employee/<int:id>')
def employee_detail(id):
    """تفاصيل الموظف"""
    from models import Employee

    employee = Employee.query.get_or_404(id)
    return render_template('employees/detail.html', employee=employee)

@app.route('/employee/<int:id>/edit', methods=['GET', 'POST'])
def edit_employee(id):
    """تعديل بيانات الموظف"""
    from models import Employee
    from forms import EmployeeForm

    employee = Employee.query.get_or_404(id)
    form = EmployeeForm(obj=employee)

    if form.validate_on_submit():
        form.populate_obj(employee)

        # Handle photo upload
        if form.photo.data:
            photo_filename = save_employee_photo(form.photo.data, employee.registration_number)
            employee.photo = photo_filename

        db.session.commit()
        flash('تم تحديث بيانات الموظف بنجاح', 'success')
        return redirect(url_for('employee_detail', id=employee.id))

    return render_template('employees/edit.html', form=form, employee=employee)

@app.route('/settings')
def settings():
    """صفحة الإعدادات"""
    return render_template('settings/index.html')

@app.route('/settings/administrative_divisions')
def administrative_divisions():
    """إدارة التقسيم الإداري"""
    from models import Wilaya

    wilayas = Wilaya.query.all()
    return render_template('settings/administrative_divisions.html', wilayas=wilayas)

@app.route('/settings/ranks')
def ranks_settings():
    """إدارة الرتب"""
    from models import Rank

    ranks = Rank.query.all()
    return render_template('settings/ranks.html', ranks=ranks)

@app.route('/settings/positions')
def positions_settings():
    """إدارة الوظائف"""
    from models import Position

    positions = Position.query.all()
    return render_template('settings/positions.html', positions=positions)

def save_employee_photo(photo, registration_number):
    """حفظ صورة الموظف مع ضغطها وتحسينها"""
    if photo:
        filename = secure_filename(f"{registration_number}_{photo.filename}")
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        
        # Save and compress image
        image = Image.open(photo)
        image.thumbnail((300, 400), Image.Resampling.LANCZOS)
        image.save(filepath, optimize=True, quality=85)
        
        return filename
    return None

def validate_social_security_number(ssn, birth_year, gender):
    """التحقق من صحة رقم الضمان الاجتماعي الجزائري"""
    if not ssn or len(ssn) != 15:
        return False
    
    try:
        # Extract components
        year_part = ssn[1:3]
        month_part = ssn[3:5]
        wilaya_part = ssn[5:7]
        gender_part = ssn[7]
        control_key = ssn[13:15]
        
        # Validate year
        if int(year_part) != int(str(birth_year)[-2:]):
            return False
        
        # Validate gender (1 for male, 2 for female)
        expected_gender = '1' if gender == 'ذكر' else '2'
        if gender_part != expected_gender:
            return False
        
        # Calculate control key
        base_number = ssn[:13]
        remainder = int(base_number) % 97
        calculated_key = 97 - remainder
        
        return int(control_key) == calculated_key
        
    except ValueError:
        return False

def validate_postal_account(account_number):
    """التحقق من صحة رقم الحساب الجاري البريدي الجزائري"""
    if not account_number or len(account_number) != 10:
        return False
    
    try:
        # Calculate control key for postal account
        base_number = account_number[:8]
        control_key = account_number[8:10]
        
        # Postal account validation algorithm
        total = 0
        for i, digit in enumerate(base_number):
            total += int(digit) * (9 - i)
        
        remainder = total % 97
        calculated_key = 97 - remainder if remainder != 0 else 0
        
        return int(control_key) == calculated_key
        
    except ValueError:
        return False

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        # Initialize default data if needed
        from models import init_default_data
        init_default_data()

    app.run(debug=True, host='0.0.0.0', port=5000)

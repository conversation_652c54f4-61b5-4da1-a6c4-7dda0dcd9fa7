{% extends "professional_base.html" %}

{% block title %}لوحة التحكم المتكاملة - نظام إدارة الجمارك الجزائرية{% endblock %}

{% block page_title %}لوحة التحكم المتكاملة{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item active">لوحة التحكم</li>
{% endblock %}

{% block content %}

<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card" style="background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%); color: white; border-radius: 15px;">
            <div class="card-body text-center py-4">
                <h1 class="h2 mb-3">
                    <i class="fas fa-building mr-3"></i>
                    مرحباً بك في نظام إدارة الجمارك الجزائرية المتكامل
                </h1>
                <p class="lead mb-0">نظام شامل ومتطور لإدارة جميع عمليات الموارد البشرية</p>
                <small class="d-block mt-2">آخر تحديث: {{ moment().format('DD/MM/YYYY - HH:mm') }}</small>
            </div>
        </div>
    </div>
</div>

<!-- Main Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card primary">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">إجمالي الموظفين</div>
                    <div class="stats-number">{{ total_employees }}</div>
                    <div class="text-xs">
                        <span class="text-success">+{{ new_employees }}</span> موظف جديد هذا الشهر
                    </div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-users stats-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card success">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">الموظفين النشطين</div>
                    <div class="stats-number">{{ active_employees }}</div>
                    <div class="text-xs">
                        <span class="text-success">{{ "%.1f"|format((active_employees/total_employees*100) if total_employees > 0 else 0) }}%</span> من الإجمالي
                    </div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-user-check stats-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card warning">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">المديريات</div>
                    <div class="stats-number">{{ total_directorates }}</div>
                    <div class="text-xs">
                        <span class="text-info">{{ total_services }}</span> مصلحة
                    </div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-building stats-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card danger">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">الذكور / الإناث</div>
                    <div class="stats-number">{{ male_count }} / {{ female_count }}</div>
                    <div class="text-xs">
                        نسبة متوازنة
                    </div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-venus-mars stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Distribution -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie mr-2"></i>
                توزيع الموظفين حسب الحالة
            </div>
            <div class="card-body">
                <div class="row">
                    {% for status, count in status_stats %}
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            {% if status == 'نشط' %}
                                <div class="h2 text-success">{{ count }}</div>
                                <div class="text-success">{{ status }}</div>
                            {% elif status == 'في إجازة' %}
                                <div class="h2 text-warning">{{ count }}</div>
                                <div class="text-warning">{{ status }}</div>
                            {% elif status == 'معلق' %}
                                <div class="h2 text-danger">{{ count }}</div>
                                <div class="text-danger">{{ status }}</div>
                            {% else %}
                                <div class="h2 text-muted">{{ count }}</div>
                                <div class="text-muted">{{ status }}</div>
                            {% endif %}
                            <div class="progress mt-2" style="height: 8px;">
                                {% set percentage = (count / total_employees * 100) if total_employees > 0 else 0 %}
                                <div class="progress-bar 
                                    {% if status == 'نشط' %}bg-success
                                    {% elif status == 'في إجازة' %}bg-warning
                                    {% elif status == 'معلق' %}bg-danger
                                    {% else %}bg-secondary{% endif %}" 
                                    style="width: {{ percentage }}%"></div>
                            </div>
                            <small class="text-muted">{{ "%.1f"|format(percentage) }}%</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt mr-2"></i>
                الإجراءات السريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ url_for('add_employee') }}" class="quick-action">
                            <div class="quick-action-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <h6 class="quick-action-title">إضافة موظف</h6>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ url_for('employees_list') }}" class="quick-action">
                            <div class="quick-action-icon">
                                <i class="fas fa-list"></i>
                            </div>
                            <h6 class="quick-action-title">قائمة الموظفين</h6>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="#" class="quick-action">
                            <div class="quick-action-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <h6 class="quick-action-title">البحث المتقدم</h6>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="#" class="quick-action">
                            <div class="quick-action-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h6 class="quick-action-title">التقارير</h6>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="#" class="quick-action">
                            <div class="quick-action-icon">
                                <i class="fas fa-file-export"></i>
                            </div>
                            <h6 class="quick-action-title">تصدير البيانات</h6>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="#" class="quick-action">
                            <div class="quick-action-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <h6 class="quick-action-title">الإعدادات</h6>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities and System Info -->
<div class="row">
    <!-- Recent Activities -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-clock mr-2"></i>
                الأنشطة الأخيرة
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>النشاط</th>
                                <th>المستخدم</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><i class="fas fa-user-plus text-success mr-1"></i> إضافة موظف جديد</td>
                                <td>أحمد محمد</td>
                                <td>اليوم 14:30</td>
                                <td><span class="badge badge-success">مكتمل</span></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-edit text-primary mr-1"></i> تحديث بيانات موظف</td>
                                <td>فاطمة علي</td>
                                <td>اليوم 12:15</td>
                                <td><span class="badge badge-success">مكتمل</span></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-calendar text-warning mr-1"></i> طلب إجازة</td>
                                <td>محمد حسن</td>
                                <td>أمس 16:45</td>
                                <td><span class="badge badge-warning">معلق</span></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-file-alt text-info mr-1"></i> تقرير شهري</td>
                                <td>سارة أحمد</td>
                                <td>أمس 10:20</td>
                                <td><span class="badge badge-primary">قيد المراجعة</span></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-database text-secondary mr-1"></i> نسخ احتياطي</td>
                                <td>النظام</td>
                                <td>أمس 02:00</td>
                                <td><span class="badge badge-success">مكتمل</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="#" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-history mr-1"></i>
                        عرض جميع الأنشطة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle mr-2"></i>
                معلومات النظام
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-primary">حالة النظام</h6>
                    <div class="d-flex justify-content-between">
                        <span>الخادم</span>
                        <span class="badge badge-success">متصل</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>قاعدة البيانات</span>
                        <span class="badge badge-success">متصلة</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>النسخ الاحتياطي</span>
                        <span class="badge badge-success">محدث</span>
                    </div>
                </div>

                <div class="mb-3">
                    <h6 class="text-success">الإحصائيات السريعة</h6>
                    <div class="d-flex justify-content-between">
                        <span>المستخدمين المتصلين</span>
                        <span class="badge badge-info">5</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>العمليات اليوم</span>
                        <span class="badge badge-primary">23</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>مساحة التخزين</span>
                        <span class="badge badge-warning">75%</span>
                    </div>
                </div>

                <div class="text-center">
                    <a href="#" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-chart-line mr-1"></i>
                        تقرير النظام
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث الوقت كل دقيقة
    setInterval(function() {
        location.reload();
    }, 60000);
    
    // تأثيرات التحويم على البطاقات
    $('.stats-card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );
    
    // تأثيرات على الإجراءات السريعة
    $('.quick-action').hover(
        function() {
            $(this).find('.quick-action-icon').addClass('animate__animated animate__pulse');
        },
        function() {
            $(this).find('.quick-action-icon').removeClass('animate__animated animate__pulse');
        }
    );
});
</script>
{% endblock %}

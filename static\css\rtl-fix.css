/* RTL and Arabic Support for AdminLTE */

/* Force RTL direction */
html, body {
    direction: rtl !important;
    text-align: right !important;
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    background-attachment: fixed !important;
}

/* Beautiful gradient backgrounds */
.main-sidebar {
    background: linear-gradient(180deg, #1e3c72 0%, #2a5298 50%, #134e5e 100%) !important;
    box-shadow: 4px 0 20px rgba(0,0,0,0.3) !important;
}

.content-wrapper {
    background: rgba(255,255,255,0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 20px 0 0 0 !important;
    margin: 10px 260px 10px 10px !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2) !important;
}

.main-header.navbar {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    box-shadow: 0 2px 20px rgba(0,0,0,0.2) !important;
    margin: 10px 260px 0 10px !important;
    border-radius: 15px 15px 0 0 !important;
}

/* Wrapper RTL */
.wrapper {
    direction: rtl !important;
}

/* Sidebar positioning for RTL */
.main-sidebar {
    right: 0 !important;
    left: auto !important;
}

.sidebar-mini .main-sidebar {
    transform: translateX(100%) !important;
}

.sidebar-mini.sidebar-collapse .main-sidebar {
    transform: translateX(calc(100% - 4.6rem)) !important;
}

/* Content wrapper margin for RTL */
.content-wrapper {
    margin-right: 250px !important;
    margin-left: 0 !important;
}

.sidebar-mini.sidebar-collapse .content-wrapper {
    margin-right: 4.6rem !important;
    margin-left: 0 !important;
}

/* Navbar positioning for RTL */
.main-header.navbar {
    margin-right: 250px !important;
    margin-left: 0 !important;
}

.sidebar-mini.sidebar-collapse .main-header.navbar {
    margin-right: 4.6rem !important;
    margin-left: 0 !important;
}

/* Sidebar toggle button */
.navbar-nav .nav-link[data-widget="pushmenu"] {
    float: left !important;
}

/* Sidebar menu items */
.nav-sidebar .nav-item .nav-link {
    text-align: right !important;
}

.nav-sidebar .nav-item .nav-link .nav-icon {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
    float: right !important;
}

.nav-sidebar .nav-item .nav-link p {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
}

/* Breadcrumb RTL */
.breadcrumb-item + .breadcrumb-item::before {
    content: "←" !important;
    margin-left: 0.5rem !important;
    margin-right: 0.5rem !important;
}

/* Form controls */
.form-control {
    text-align: right !important;
}

/* Tables */
.table th, .table td {
    text-align: right !important;
}

/* Buttons in tables */
.table .btn-group {
    float: left !important;
}

/* Modal */
.modal-header .close {
    margin: -1rem auto -1rem -1rem !important;
}

/* Pagination */
.pagination {
    direction: ltr !important;
}

/* Cards */
.card-tools {
    float: left !important;
}

/* Info boxes */
.info-box-icon {
    border-radius: 0 0.25rem 0.25rem 0 !important;
    float: right !important;
}

.info-box-content {
    padding: 5px 10px 5px 90px !important;
}

/* Small boxes */
.small-box .icon {
    right: auto !important;
    left: 10px !important;
}

.small-box-footer {
    text-align: center !important;
}

/* Dropdown menus */
.dropdown-menu {
    right: 0 !important;
    left: auto !important;
}

/* Alerts */
.alert-dismissible .close {
    right: auto !important;
    left: 0 !important;
    padding: 0.75rem 1.25rem 0.75rem 0.75rem !important;
}

/* Input groups */
.input-group-prepend {
    margin-left: -1px !important;
    margin-right: 0 !important;
}

.input-group-append {
    margin-right: -1px !important;
    margin-left: 0 !important;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .main-sidebar {
        transform: translateX(100%) !important;
    }
    
    .sidebar-open .main-sidebar {
        transform: translateX(0) !important;
    }
    
    .content-wrapper,
    .main-header.navbar {
        margin-right: 0 !important;
        margin-left: 0 !important;
    }
}

/* Beautiful Cards */
.card {
    border-radius: 20px !important;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1) !important;
    border: none !important;
    backdrop-filter: blur(10px) !important;
    background: rgba(255,255,255,0.9) !important;
    transition: all 0.3s ease !important;
    overflow: hidden !important;
}

.card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 40px rgba(0,0,0,0.2) !important;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
    padding: 20px !important;
    font-weight: 600 !important;
    position: relative !important;
}

.card-header::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 3px !important;
    background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3) !important;
}

/* Beautiful Buttons */
.btn {
    border-radius: 25px !important;
    font-weight: 600 !important;
    padding: 12px 30px !important;
    transition: all 0.3s ease !important;
    border: none !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent) !important;
    transition: left 0.5s !important;
}

.btn:hover::before {
    left: 100% !important;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
}

.btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6) !important;
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
    box-shadow: 0 4px 15px rgba(86, 171, 47, 0.4) !important;
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4) !important;
}

.btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4) !important;
}

/* Beautiful Small Boxes */
.small-box {
    border-radius: 20px !important;
    box-shadow: 0 8px 32px rgba(0,0,0,0.15) !important;
    transition: all 0.3s ease !important;
    overflow: hidden !important;
    position: relative !important;
}

.small-box::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 4px !important;
    background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3) !important;
}

.small-box:hover {
    transform: translateY(-8px) scale(1.02) !important;
    box-shadow: 0 20px 40px rgba(0,0,0,0.25) !important;
}

.small-box .inner {
    padding: 25px !important;
}

.small-box .icon {
    font-size: 80px !important;
    opacity: 0.8 !important;
    transition: all 0.3s ease !important;
}

.small-box:hover .icon {
    transform: scale(1.1) rotate(5deg) !important;
    opacity: 1 !important;
}

/* Beautiful Navigation */
.nav-sidebar .nav-link {
    border-radius: 15px !important;
    margin: 5px 15px !important;
    padding: 15px 20px !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

.nav-sidebar .nav-link::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent) !important;
    transition: left 0.5s !important;
}

.nav-sidebar .nav-link:hover::before {
    left: 100% !important;
}

.nav-sidebar .nav-link:hover {
    background: rgba(255,255,255,0.2) !important;
    transform: translateX(-10px) !important;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3) !important;
}

.nav-sidebar .nav-link.active {
    background: rgba(255,255,255,0.3) !important;
    box-shadow: 0 5px 20px rgba(0,0,0,0.4) !important;
    transform: translateX(-10px) !important;
}

/* Arabic number support */
.arabic-numbers {
    font-feature-settings: "lnum" 1;
}

/* Fix for select2 RTL */
.select2-container--default .select2-selection--single {
    text-align: right !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    right: auto !important;
    left: 1px !important;
}

/* Fix for DataTables RTL */
.dataTables_wrapper .dataTables_filter {
    float: left !important;
    text-align: left !important;
}

.dataTables_wrapper .dataTables_length {
    float: right !important;
}

.dataTables_wrapper .dataTables_info {
    float: right !important;
}

.dataTables_wrapper .dataTables_paginate {
    float: left !important;
}

/* Beautiful Tables */
.table {
    border-radius: 15px !important;
    overflow: hidden !important;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1) !important;
    background: white !important;
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
    padding: 20px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
}

.table tbody tr {
    transition: all 0.3s ease !important;
}

.table tbody tr:hover {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)) !important;
    transform: scale(1.01) !important;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1) !important;
}

.table tbody td {
    padding: 15px 20px !important;
    border: none !important;
    border-bottom: 1px solid rgba(0,0,0,0.05) !important;
}

/* Beautiful Forms */
.form-control {
    border-radius: 15px !important;
    border: 2px solid #e9ecef !important;
    padding: 15px 20px !important;
    transition: all 0.3s ease !important;
    background: rgba(255,255,255,0.9) !important;
    backdrop-filter: blur(10px) !important;
}

.form-control:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
    transform: translateY(-2px) !important;
    background: white !important;
}

.form-group label {
    font-weight: 600 !important;
    color: #495057 !important;
    margin-bottom: 10px !important;
}

/* Beautiful Modals */
.modal-content {
    border-radius: 25px !important;
    border: none !important;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3) !important;
    backdrop-filter: blur(20px) !important;
    background: rgba(255,255,255,0.95) !important;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 25px 25px 0 0 !important;
    padding: 25px !important;
}

.modal-body {
    padding: 30px !important;
}

.modal-footer {
    border: none !important;
    padding: 20px 30px 30px !important;
}

/* Beautiful Alerts */
.alert {
    border-radius: 15px !important;
    border: none !important;
    padding: 20px !important;
    font-weight: 500 !important;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1) !important;
}

.alert-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
    color: white !important;
}

.alert-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
    color: white !important;
}

.alert-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    color: white !important;
}

.alert-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

/* Beautiful Breadcrumb */
.breadcrumb {
    background: rgba(255,255,255,0.9) !important;
    border-radius: 15px !important;
    padding: 15px 20px !important;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1) !important;
    backdrop-filter: blur(10px) !important;
}

.breadcrumb-item a {
    color: #667eea !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.breadcrumb-item a:hover {
    color: #764ba2 !important;
    transform: translateX(-3px) !important;
}

/* Beautiful Pagination */
.pagination .page-link {
    border-radius: 10px !important;
    margin: 0 3px !important;
    border: none !important;
    padding: 10px 15px !important;
    background: rgba(255,255,255,0.9) !important;
    color: #667eea !important;
    transition: all 0.3s ease !important;
}

.pagination .page-link:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4) !important;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4) !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.8), 0 0 30px rgba(118, 75, 162, 0.6);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out !important;
}

.small-box {
    animation: slideInRight 0.8s ease-out !important;
}

/* Print styles */
@media print {
    .main-sidebar,
    .main-header,
    .btn,
    .no-print {
        display: none !important;
    }

    .content-wrapper {
        margin: 0 !important;
    }

    body {
        direction: rtl !important;
        text-align: right !important;
    }
}

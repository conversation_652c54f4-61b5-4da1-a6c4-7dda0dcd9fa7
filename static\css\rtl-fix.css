/* RTL and Arabic Support for AdminLTE */

/* Force RTL direction */
html, body {
    direction: rtl !important;
    text-align: right !important;
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Wrapper RTL */
.wrapper {
    direction: rtl !important;
}

/* Sidebar positioning for RTL */
.main-sidebar {
    right: 0 !important;
    left: auto !important;
}

.sidebar-mini .main-sidebar {
    transform: translateX(100%) !important;
}

.sidebar-mini.sidebar-collapse .main-sidebar {
    transform: translateX(calc(100% - 4.6rem)) !important;
}

/* Content wrapper margin for RTL */
.content-wrapper {
    margin-right: 250px !important;
    margin-left: 0 !important;
}

.sidebar-mini.sidebar-collapse .content-wrapper {
    margin-right: 4.6rem !important;
    margin-left: 0 !important;
}

/* Navbar positioning for RTL */
.main-header.navbar {
    margin-right: 250px !important;
    margin-left: 0 !important;
}

.sidebar-mini.sidebar-collapse .main-header.navbar {
    margin-right: 4.6rem !important;
    margin-left: 0 !important;
}

/* Sidebar toggle button */
.navbar-nav .nav-link[data-widget="pushmenu"] {
    float: left !important;
}

/* Sidebar menu items */
.nav-sidebar .nav-item .nav-link {
    text-align: right !important;
}

.nav-sidebar .nav-item .nav-link .nav-icon {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
    float: right !important;
}

.nav-sidebar .nav-item .nav-link p {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
}

/* Breadcrumb RTL */
.breadcrumb-item + .breadcrumb-item::before {
    content: "←" !important;
    margin-left: 0.5rem !important;
    margin-right: 0.5rem !important;
}

/* Form controls */
.form-control {
    text-align: right !important;
}

/* Tables */
.table th, .table td {
    text-align: right !important;
}

/* Buttons in tables */
.table .btn-group {
    float: left !important;
}

/* Modal */
.modal-header .close {
    margin: -1rem auto -1rem -1rem !important;
}

/* Pagination */
.pagination {
    direction: ltr !important;
}

/* Cards */
.card-tools {
    float: left !important;
}

/* Info boxes */
.info-box-icon {
    border-radius: 0 0.25rem 0.25rem 0 !important;
    float: right !important;
}

.info-box-content {
    padding: 5px 10px 5px 90px !important;
}

/* Small boxes */
.small-box .icon {
    right: auto !important;
    left: 10px !important;
}

.small-box-footer {
    text-align: center !important;
}

/* Dropdown menus */
.dropdown-menu {
    right: 0 !important;
    left: auto !important;
}

/* Alerts */
.alert-dismissible .close {
    right: auto !important;
    left: 0 !important;
    padding: 0.75rem 1.25rem 0.75rem 0.75rem !important;
}

/* Input groups */
.input-group-prepend {
    margin-left: -1px !important;
    margin-right: 0 !important;
}

.input-group-append {
    margin-right: -1px !important;
    margin-left: 0 !important;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .main-sidebar {
        transform: translateX(100%) !important;
    }
    
    .sidebar-open .main-sidebar {
        transform: translateX(0) !important;
    }
    
    .content-wrapper,
    .main-header.navbar {
        margin-right: 0 !important;
        margin-left: 0 !important;
    }
}

/* Custom enhancements */
.customs-gradient {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    color: white !important;
}

.customs-card {
    border-radius: 15px !important;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
    border: none !important;
}

.customs-btn {
    border-radius: 8px !important;
    font-weight: 600 !important;
    padding: 8px 20px !important;
}

/* Arabic number support */
.arabic-numbers {
    font-feature-settings: "lnum" 1;
}

/* Fix for select2 RTL */
.select2-container--default .select2-selection--single {
    text-align: right !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    right: auto !important;
    left: 1px !important;
}

/* Fix for DataTables RTL */
.dataTables_wrapper .dataTables_filter {
    float: left !important;
    text-align: left !important;
}

.dataTables_wrapper .dataTables_length {
    float: right !important;
}

.dataTables_wrapper .dataTables_info {
    float: right !important;
}

.dataTables_wrapper .dataTables_paginate {
    float: left !important;
}

/* Print styles */
@media print {
    .main-sidebar,
    .main-header,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .content-wrapper {
        margin: 0 !important;
    }
    
    body {
        direction: rtl !important;
        text-align: right !important;
    }
}

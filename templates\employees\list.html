{% extends "base.html" %}

{% block title %}قائمة الموظفين - برنامج تسيير مستخدمي الجمارك الجزائرية{% endblock %}

{% block page_title %}قائمة الموظفين{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item">إدارة الموظفين</li>
<li class="breadcrumb-item active">قائمة الموظفين</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">قائمة الموظفين</h3>
                <div class="card-tools">
                    <a href="{{ url_for('add_employee') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> إضافة موظف جديد
                    </a>
                </div>
            </div>
            <!-- /.card-header -->
            
            <div class="card-body">
                <!-- Search Form -->
                <form method="GET" class="mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" placeholder="البحث برقم التسجيل، الاسم أو اللقب..." value="{{ search }}">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-control">
                                <option value="">جميع الحالات</option>
                                <option value="نشط">نشط</option>
                                <option value="تحويل">تحويل</option>
                                <option value="موقف">موقف</option>
                                <option value="استيداع">استيداع</option>
                                <option value="منتدب">منتدب</option>
                                <option value="متوفي">متوفي</option>
                                <option value="مفصول">مفصول</option>
                                <option value="مستقيل">مستقيل</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-filter"></i> تصفية
                            </button>
                            <a href="{{ url_for('employees_list') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </div>
                </form>
                
                <!-- Employees Table -->
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>رقم التسجيل</th>
                                <th>الاسم الكامل</th>
                                <th>الجنس</th>
                                <th>تاريخ الميلاد</th>
                                <th>الرتبة الحالية</th>
                                <th>الوظيفة الحالية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees.items %}
                            <tr>
                                <td class="text-center">
                                    {% if employee.photo %}
                                        <img src="{{ url_for('static', filename='uploads/' + employee.photo) }}" 
                                             alt="صورة {{ employee.full_name }}" 
                                             class="img-thumbnail" 
                                             style="width: 50px; height: 60px; object-fit: cover;">
                                    {% else %}
                                        <div class="bg-secondary d-flex align-items-center justify-content-center" 
                                             style="width: 50px; height: 60px; border-radius: 4px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>{{ employee.registration_number }}</td>
                                <td>
                                    <strong>{{ employee.full_name }}</strong>
                                    {% if employee.nom_fr and employee.prenom_fr %}
                                        <br><small class="text-muted">{{ employee.prenom_fr }} {{ employee.nom_fr }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if employee.gender == 'ذكر' %}
                                        <span class="badge badge-primary">{{ employee.gender }}</span>
                                    {% else %}
                                        <span class="badge badge-info">{{ employee.gender }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ employee.birth_date.strftime('%Y-%m-%d') }}
                                    <br><small class="text-muted">{{ employee.age }} سنة</small>
                                </td>
                                <td>{{ employee.current_rank.name if employee.current_rank else 'غير محدد' }}</td>
                                <td>{{ employee.current_position.name if employee.current_position else 'غير محدد' }}</td>
                                <td>
                                    {% if employee.status == 'نشط' %}
                                        <span class="badge badge-success">{{ employee.status }}</span>
                                    {% elif employee.status in ['تحويل', 'منتدب'] %}
                                        <span class="badge badge-warning">{{ employee.status }}</span>
                                    {% elif employee.status in ['موقف', 'استيداع'] %}
                                        <span class="badge badge-info">{{ employee.status }}</span>
                                    {% elif employee.status in ['متوفي', 'مفصول', 'مستقيل'] %}
                                        <span class="badge badge-danger">{{ employee.status }}</span>
                                    {% else %}
                                        <span class="badge badge-secondary">{{ employee.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('employee_detail', id=employee.id) }}" 
                                           class="btn btn-info btn-sm" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('edit_employee', id=employee.id) }}" 
                                           class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" 
                                                onclick="confirmDelete({{ employee.id }})" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="9" class="text-center">
                                    <div class="py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">لا توجد بيانات موظفين</h5>
                                        <p class="text-muted">لم يتم العثور على أي موظفين مطابقين لمعايير البحث.</p>
                                        <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> إضافة موظف جديد
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if employees.pages > 1 %}
                <nav aria-label="تصفح الصفحات">
                    <ul class="pagination justify-content-center">
                        {% if employees.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees_list', page=employees.prev_num, search=search) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in employees.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != employees.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('employees_list', page=page_num, search=search) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if employees.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees_list', page=employees.next_num, search=search) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
            </div>
            <!-- /.card-body -->
            
            <div class="card-footer">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="dataTables_info">
                            عرض {{ employees.per_page * (employees.page - 1) + 1 }} إلى 
                            {{ employees.per_page * (employees.page - 1) + employees.items|length }} من 
                            {{ employees.total }} موظف
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="float-right">
                            <button type="button" class="btn btn-success btn-sm">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                            <button type="button" class="btn btn-danger btn-sm">
                                <i class="fas fa-file-pdf"></i> تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="إغلاق">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من أنك تريد حذف هذا الموظف؟ هذا الإجراء لا يمكن التراجع عنه.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(employeeId) {
    $('#deleteForm').attr('action', '/employee/' + employeeId + '/delete');
    $('#deleteModal').modal('show');
}

// Auto-submit form when status filter changes
$('select[name="status"]').change(function() {
    $(this).closest('form').submit();
});
</script>
{% endblock %}

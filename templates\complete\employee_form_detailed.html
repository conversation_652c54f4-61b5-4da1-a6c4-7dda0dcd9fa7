{% extends "professional_base.html" %}

{% block title %}{{ title }} - نظام إدارة الجمارك الجزائرية{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item">إدارة الموظفين</li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">
            <i class="fas fa-user-{{ 'edit' if employee else 'plus' }} mr-2"></i>
            {{ title }}
        </h1>
        <p class="text-muted">{{ 'تعديل بيانات الموظف الحالي' if employee else 'إضافة موظف جديد إلى النظام' }}</p>
    </div>
    <div class="col-md-4 text-left">
        <a href="{{ url_for('employees_list') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right mr-1"></i>
            العودة للقائمة
        </a>
        {% if employee %}
        <a href="{{ url_for('employee_detail', id=employee.id) }}" class="btn btn-info">
            <i class="fas fa-eye mr-1"></i>
            عرض التفاصيل
        </a>
        {% endif %}
    </div>
</div>

<!-- Progress Steps -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="progress-steps">
                    <div class="step active" data-step="1">
                        <div class="step-number">1</div>
                        <div class="step-title">البيانات الأساسية</div>
                    </div>
                    <div class="step" data-step="2">
                        <div class="step-number">2</div>
                        <div class="step-title">بيانات الاتصال</div>
                    </div>
                    <div class="step" data-step="3">
                        <div class="step-number">3</div>
                        <div class="step-title">البيانات المهنية</div>
                    </div>
                    <div class="step" data-step="4">
                        <div class="step-number">4</div>
                        <div class="step-title">الوثائق الرسمية</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<form method="POST" enctype="multipart/form-data" id="employeeForm">
    {{ form.hidden_tag() }}
    
    <!-- Step 1: البيانات الأساسية -->
    <div class="form-step active" id="step-1">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user mr-2"></i>
                            1- البيانات الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.registration_number.label(class="form-label required") }}
                                    {{ form.registration_number(class="form-control" + (" is-invalid" if form.registration_number.errors else "")) }}
                                    {% if form.registration_number.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.registration_number.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">6 أرقام فقط (مثال: 123456)</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.last_name.label(class="form-label required") }}
                                    {{ form.last_name(class="form-control" + (" is-invalid" if form.last_name.errors else "")) }}
                                    {% if form.last_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.last_name.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.first_name.label(class="form-label required") }}
                                    {{ form.first_name(class="form-control" + (" is-invalid" if form.first_name.errors else "")) }}
                                    {% if form.first_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.first_name.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.gender.label(class="form-label required") }}
                                    {{ form.gender(class="form-control" + (" is-invalid" if form.gender.errors else "")) }}
                                    {% if form.gender.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.gender.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.nom_fr.label(class="form-label") }}
                                    {{ form.nom_fr(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.prenom_fr.label(class="form-label") }}
                                    {{ form.prenom_fr(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.birth_date.label(class="form-label required") }}
                                    {{ form.birth_date(class="form-control" + (" is-invalid" if form.birth_date.errors else ""), max=moment().strftime('%Y-%m-%d')) }}
                                    {% if form.birth_date.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.birth_date.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">العمر: 19-65 سنة</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.blood_type.label(class="form-label") }}
                                    {{ form.blood_type(class="form-control") }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.birth_wilaya_id.label(class="form-label") }}
                                    {{ form.birth_wilaya_id(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.birth_commune_id.label(class="form-label") }}
                                    {{ form.birth_commune_id(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.marital_status.label(class="form-label") }}
                                    {{ form.marital_status(class="form-control", id="marital_status") }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4" id="children_count_group">
                                <div class="form-group">
                                    {{ form.children_count.label(class="form-label") }}
                                    {{ form.children_count(class="form-control" + (" is-invalid" if form.children_count.errors else ""), min="0", max="20") }}
                                    {% if form.children_count.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.children_count.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">لا يظهر للأعزب</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.dependents_count.label(class="form-label") }}
                                    {{ form.dependents_count(class="form-control", min="0", max="20") }}
                                    <small class="form-text text-muted">العدد الإجمالي للمعالين</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.practiced_sport.label(class="form-label") }}
                                    {{ form.practiced_sport(class="form-control", placeholder="مثال: كرة القدم، السباحة") }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="photo" class="form-label">صورة الموظف</label>
                                    {{ form.photo(class="form-control-file", accept="image/*") }}
                                    <small class="form-text text-muted">اختر صورة شخصية للموظف (JPG, PNG) - سيتم تعديل الحجم تلقائياً</small>
                                    {% if employee and employee.photo %}
                                    <div class="mt-2">
                                        <img src="{{ url_for('static', filename='photos/' + employee.photo) }}" 
                                             class="img-thumbnail" width="100" height="100" alt="الصورة الحالية">
                                        <br><small class="text-muted">الصورة الحالية</small>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 2: بيانات الاتصال -->
    <div class="form-step" id="step-2">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-address-book mr-2"></i>
                            2- بيانات الاتصال
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.phone1.label(class="form-label") }}
                                    {{ form.phone1(class="form-control", placeholder="0555123456") }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.phone2.label(class="form-label") }}
                                    {{ form.phone2(class="form-control", placeholder="0666789123") }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.email.label(class="form-label") }}
                                    {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else ""), placeholder="<EMAIL>") }}
                                    {% if form.email.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.email.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.primary_address.label(class="form-label") }}
                                    {{ form.primary_address(class="form-control", rows="3", placeholder="العنوان الكامل مع الحي والولاية") }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.secondary_address.label(class="form-label") }}
                                    {{ form.secondary_address(class="form-control", rows="3", placeholder="عنوان إضافي (اختياري)") }}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.emergency_contact_name.label(class="form-label") }}
                                    {{ form.emergency_contact_name(class="form-control", placeholder="الاسم الكامل للشخص المتصل به") }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.emergency_contact_address.label(class="form-label") }}
                                    {{ form.emergency_contact_address(class="form-control", rows="3", placeholder="عنوان الشخص المتصل به") }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 3: البيانات المهنية -->
    <div class="form-step" id="step-3">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-briefcase mr-2"></i>
                            3- البيانات المهنية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.employee_status.label(class="form-label required") }}
                                    {{ form.employee_status(class="form-control" + (" is-invalid" if form.employee_status.errors else "")) }}
                                    {% if form.employee_status.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.employee_status.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.corps_id.label(class="form-label required") }}
                                    {{ form.corps_id(class="form-control" + (" is-invalid" if form.corps_id.errors else "")) }}
                                    {% if form.corps_id.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.corps_id.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.current_rank_id.label(class="form-label required") }}
                                    {{ form.current_rank_id(class="form-control" + (" is-invalid" if form.current_rank_id.errors else "")) }}
                                    {% if form.current_rank_id.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.current_rank_id.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.rank_promotion_date.label(class="form-label") }}
                                    {{ form.rank_promotion_date(class="form-control") }}
                                    <small class="form-text text-muted">تاريخ الترقية في الرتبة الحالية</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.current_position_id.label(class="form-label required") }}
                                    {{ form.current_position_id(class="form-control" + (" is-invalid" if form.current_position_id.errors else "")) }}
                                    {% if form.current_position_id.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.current_position_id.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.position_assignment_date.label(class="form-label") }}
                                    {{ form.position_assignment_date(class="form-control") }}
                                    <small class="form-text text-muted">تاريخ التعيين في الوظيفة الحالية</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.directorate_id.label(class="form-label required") }}
                                    {{ form.directorate_id(class="form-control" + (" is-invalid" if form.directorate_id.errors else "")) }}
                                    {% if form.directorate_id.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.directorate_id.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.service_id.label(class="form-label") }}
                                    {{ form.service_id(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.assignment_location_id.label(class="form-label") }}
                                    {{ form.assignment_location_id(class="form-control") }}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.hiring_date.label(class="form-label required") }}
                                    {{ form.hiring_date(class="form-control" + (" is-invalid" if form.hiring_date.errors else ""), max=moment().strftime('%Y-%m-%d')) }}
                                    {% if form.hiring_date.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.hiring_date.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">تاريخ الدخول في إدارة الجمارك</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.hiring_rank_id.label(class="form-label") }}
                                    {{ form.hiring_rank_id(class="form-control") }}
                                    <small class="form-text text-muted">الرتبة عند التوظيف</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 4: الوثائق الرسمية -->
    <div class="form-step" id="step-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-id-card mr-2"></i>
                            4- الوثائق والأرقام الرسمية
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- الأرقام الأساسية -->
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-hashtag mr-2"></i>
                            الأرقام الأساسية
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.social_security_number.label(class="form-label") }}
                                    {{ form.social_security_number(class="form-control" + (" is-invalid" if form.social_security_number.errors else "")) }}
                                    {% if form.social_security_number.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.social_security_number.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">15 رقم - سيتم التحقق من صحته تلقائياً</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.postal_account_number.label(class="form-label") }}
                                    {{ form.postal_account_number(class="form-control" + (" is-invalid" if form.postal_account_number.errors else "")) }}
                                    {% if form.postal_account_number.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.postal_account_number.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">رقم الحساب الجاري البريدي مع المفتاح</small>
                                </div>
                            </div>
                        </div>

                        <!-- بطاقة التعريف الوطنية -->
                        <h6 class="text-success mb-3 mt-4">
                            <i class="fas fa-id-card mr-2"></i>
                            بطاقة التعريف الوطنية
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.national_id_number.label(class="form-label") }}
                                    {{ form.national_id_number(class="form-control" + (" is-invalid" if form.national_id_number.errors else "")) }}
                                    {% if form.national_id_number.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.national_id_number.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">18 رقم - سيتم التحقق من التطابق مع البيانات</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.national_id_issue_date.label(class="form-label") }}
                                    {{ form.national_id_issue_date(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.national_id_issue_place_id.label(class="form-label") }}
                                    {{ form.national_id_issue_place_id(class="form-control") }}
                                </div>
                            </div>
                        </div>

                        <!-- البطاقة المهنية -->
                        <h6 class="text-warning mb-3 mt-4">
                            <i class="fas fa-id-badge mr-2"></i>
                            البطاقة المهنية
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.professional_card_number.label(class="form-label") }}
                                    {{ form.professional_card_number(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.professional_card_issue_date.label(class="form-label") }}
                                    {{ form.professional_card_issue_date(class="form-control") }}
                                </div>
                            </div>
                        </div>

                        <!-- رخصة السياقة -->
                        <h6 class="text-info mb-3 mt-4">
                            <i class="fas fa-car mr-2"></i>
                            رخصة السياقة
                        </h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.driving_license_number.label(class="form-label") }}
                                    {{ form.driving_license_number(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.driving_license_category.label(class="form-label") }}
                                    {{ form.driving_license_category(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.driving_license_issue_date.label(class="form-label") }}
                                    {{ form.driving_license_issue_date(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.driving_license_issue_place_id.label(class="form-label") }}
                                    {{ form.driving_license_issue_place_id(class="form-control") }}
                                </div>
                            </div>
                        </div>

                        <!-- بطاقة التعاضدية -->
                        <h6 class="text-secondary mb-3 mt-4">
                            <i class="fas fa-heart mr-2"></i>
                            بطاقة التعاضدية
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.mutual_card_number.label(class="form-label") }}
                                    {{ form.mutual_card_number(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.mutual_card_issue_date.label(class="form-label") }}
                                    {{ form.mutual_card_issue_date(class="form-control") }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <button type="button" class="btn btn-secondary" id="prevBtn" onclick="changeStep(-1)" style="display: none;">
                        <i class="fas fa-arrow-right mr-2"></i>
                        السابق
                    </button>
                    <button type="button" class="btn btn-primary" id="nextBtn" onclick="changeStep(1)">
                        التالي
                        <i class="fas fa-arrow-left mr-2"></i>
                    </button>
                    <button type="submit" class="btn btn-success btn-lg" id="submitBtn" style="display: none;">
                        <i class="fas fa-save mr-2"></i>
                        {{ 'تحديث البيانات' if employee else 'حفظ الموظف' }}
                    </button>
                    <a href="{{ url_for('employees_list') }}" class="btn btn-secondary mr-3">
                        <i class="fas fa-times mr-2"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>

{% endblock %}

{% block extra_js %}
<script>
let currentStep = 1;
const totalSteps = 4;

function showStep(step) {
    // إخفاء جميع الخطوات
    $('.form-step').removeClass('active');
    $('.step').removeClass('active');
    
    // إظهار الخطوة الحالية
    $(`#step-${step}`).addClass('active');
    $(`.step[data-step="${step}"]`).addClass('active');
    
    // تحديث الأزرار
    $('#prevBtn').toggle(step > 1);
    $('#nextBtn').toggle(step < totalSteps);
    $('#submitBtn').toggle(step === totalSteps);
}

function changeStep(direction) {
    const newStep = currentStep + direction;
    
    if (newStep >= 1 && newStep <= totalSteps) {
        // التحقق من صحة الخطوة الحالية قبل الانتقال
        if (direction > 0 && !validateCurrentStep()) {
            return;
        }
        
        currentStep = newStep;
        showStep(currentStep);
    }
}

function validateCurrentStep() {
    let isValid = true;
    const currentStepElement = $(`#step-${currentStep}`);
    
    // التحقق من الحقول المطلوبة في الخطوة الحالية
    currentStepElement.find('input[required], select[required]').each(function() {
        if (!$(this).val()) {
            $(this).addClass('is-invalid');
            isValid = false;
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // تحقق خاص للخطوة الأولى
    if (currentStep === 1) {
        // التحقق من رقم التسجيل
        const regNumber = $('#registration_number').val();
        if (regNumber && (regNumber.length !== 6 || !regNumber.match(/^\d{6}$/))) {
            $('#registration_number').addClass('is-invalid');
            isValid = false;
        }
        
        // التحقق من العمر
        const birthDate = $('#birth_date').val();
        if (birthDate) {
            const age = calculateAge(new Date(birthDate));
            if (age < 19 || age > 65) {
                $('#birth_date').addClass('is-invalid');
                isValid = false;
            }
        }
    }
    
    if (!isValid) {
        alert('يرجى تصحيح الأخطاء قبل المتابعة');
    }
    
    return isValid;
}

function calculateAge(birthDate) {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }
    
    return age;
}

$(document).ready(function() {
    // إخفاء/إظهار حقل عدد الأبناء حسب الحالة العائلية
    $('#marital_status').change(function() {
        const status = $(this).val();
        if (status === 'أعزب') {
            $('#children_count_group').hide();
            $('#children_count').val(0);
        } else {
            $('#children_count_group').show();
        }
    });
    
    // تطبيق القاعدة عند تحميل الصفحة
    $('#marital_status').trigger('change');
    
    // تحديث البلديات عند تغيير الولاية
    $('#birth_wilaya_id').change(function() {
        const wilayaId = $(this).val();
        if (wilayaId) {
            // هنا يمكن إضافة AJAX لجلب البلديات
            $('#birth_commune_id').html('<option value="">اختر البلدية</option>');
        }
    });
    
    // التحقق من النموذج قبل الإرسال
    $('#employeeForm').submit(function(e) {
        if (!validateCurrentStep()) {
            e.preventDefault();
        }
    });
    
    // تفعيل tooltips
    $('[title]').tooltip();
    
    // إظهار الخطوة الأولى
    showStep(1);
});
</script>

<style>
.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.step.active {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background-color: #007bff;
    color: white;
}

.step-title {
    font-size: 12px;
    text-align: center;
    color: #6c757d;
}

.step.active .step-title {
    color: #007bff;
    font-weight: bold;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    left: calc(50% + 20px);
    width: calc(100% - 40px);
    height: 2px;
    background-color: #dee2e6;
    z-index: -1;
}

.form-step {
    display: none;
}

.form-step.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.required::after {
    content: " *";
    color: red;
}

.form-control.is-invalid {
    border-color: #dc3545;
}

@media (max-width: 768px) {
    .progress-steps {
        flex-direction: column;
        gap: 10px;
    }
    
    .step:not(:last-child)::after {
        display: none;
    }
}
</style>
{% endblock %}

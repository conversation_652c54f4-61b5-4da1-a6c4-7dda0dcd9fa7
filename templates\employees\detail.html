{% extends "base.html" %}

{% block title %}{{ employee.full_name }} - تفاصيل الموظف{% endblock %}

{% block page_title %}تفاصيل الموظف{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item">إدارة الموظفين</li>
<li class="breadcrumb-item"><a href="{{ url_for('employees_list') }}">قائمة الموظفين</a></li>
<li class="breadcrumb-item active">{{ employee.full_name }}</li>
{% endblock %}

{% block content %}
<div class="row">
    <!-- معلومات أساسية -->
    <div class="col-md-4">
        <div class="card card-primary card-outline">
            <div class="card-body box-profile">
                <div class="text-center">
                    {% if employee.photo %}
                        <img class="profile-user-img img-fluid img-circle employee-photo"
                             src="{{ url_for('static', filename='uploads/' + employee.photo) }}"
                             alt="صورة {{ employee.full_name }}"
                             style="width: 150px; height: 150px; object-fit: cover;">
                    {% else %}
                        <div class="profile-user-img img-fluid img-circle bg-secondary d-flex align-items-center justify-content-center"
                             style="width: 150px; height: 150px; margin: 0 auto;">
                            <i class="fas fa-user fa-4x text-white"></i>
                        </div>
                    {% endif %}
                </div>

                <h3 class="profile-username text-center">{{ employee.full_name }}</h3>
                {% if employee.nom_fr and employee.prenom_fr %}
                    <p class="text-muted text-center">{{ employee.prenom_fr }} {{ employee.nom_fr }}</p>
                {% endif %}

                <p class="text-muted text-center">{{ employee.current_position.name if employee.current_position else 'غير محدد' }}</p>

                <ul class="list-group list-group-unbordered mb-3">
                    <li class="list-group-item">
                        <b>رقم التسجيل</b> <span class="float-left">{{ employee.registration_number }}</span>
                    </li>
                    <li class="list-group-item">
                        <b>الحالة</b> 
                        <span class="float-left">
                            {% if employee.status == 'نشط' %}
                                <span class="badge badge-success">{{ employee.status }}</span>
                            {% elif employee.status in ['تحويل', 'منتدب'] %}
                                <span class="badge badge-warning">{{ employee.status }}</span>
                            {% elif employee.status in ['موقف', 'استيداع'] %}
                                <span class="badge badge-info">{{ employee.status }}</span>
                            {% elif employee.status in ['متوفي', 'مفصول', 'مستقيل'] %}
                                <span class="badge badge-danger">{{ employee.status }}</span>
                            {% else %}
                                <span class="badge badge-secondary">{{ employee.status }}</span>
                            {% endif %}
                        </span>
                    </li>
                    <li class="list-group-item">
                        <b>العمر</b> <span class="float-left">{{ employee.age }} سنة</span>
                    </li>
                    <li class="list-group-item">
                        <b>الجنس</b> 
                        <span class="float-left">
                            {% if employee.gender == 'ذكر' %}
                                <span class="badge badge-primary">{{ employee.gender }}</span>
                            {% else %}
                                <span class="badge badge-info">{{ employee.gender }}</span>
                            {% endif %}
                        </span>
                    </li>
                </ul>

                <div class="row">
                    <div class="col-6">
                        <a href="{{ url_for('edit_employee', id=employee.id) }}" class="btn btn-warning btn-block">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-danger btn-block" onclick="confirmDelete({{ employee.id }})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الاتصال -->
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-phone"></i> معلومات الاتصال
                </h3>
            </div>
            <div class="card-body">
                <strong><i class="fas fa-phone mr-1"></i> الهاتف 1</strong>
                <p class="text-muted">{{ employee.phone1 or 'غير محدد' }}</p>
                <hr>

                <strong><i class="fas fa-phone mr-1"></i> الهاتف 2</strong>
                <p class="text-muted">{{ employee.phone2 or 'غير محدد' }}</p>
                <hr>

                <strong><i class="fas fa-envelope mr-1"></i> البريد الإلكتروني</strong>
                <p class="text-muted">{{ employee.email or 'غير محدد' }}</p>
                <hr>

                <strong><i class="fas fa-map-marker-alt mr-1"></i> العنوان الرئيسي</strong>
                <p class="text-muted">{{ employee.primary_address or 'غير محدد' }}</p>
                <hr>

                <strong><i class="fas fa-user-friends mr-1"></i> جهة الاتصال في حالة الطوارئ</strong>
                <p class="text-muted">{{ employee.emergency_contact_name or 'غير محدد' }}</p>
            </div>
        </div>
    </div>

    <!-- التفاصيل الرئيسية -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header p-2">
                <ul class="nav nav-pills">
                    <li class="nav-item"><a class="nav-link active" href="#basic" data-toggle="tab">البيانات الأساسية</a></li>
                    <li class="nav-item"><a class="nav-link" href="#professional" data-toggle="tab">البيانات المهنية</a></li>
                    <li class="nav-item"><a class="nav-link" href="#documents" data-toggle="tab">الوثائق</a></li>
                    <li class="nav-item"><a class="nav-link" href="#family" data-toggle="tab">الأسرة</a></li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content">
                    <!-- البيانات الأساسية -->
                    <div class="active tab-pane" id="basic">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>اللقب</label>
                                    <p class="form-control-static">{{ employee.last_name }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>الاسم</label>
                                    <p class="form-control-static">{{ employee.first_name }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>تاريخ الميلاد</label>
                                    <p class="form-control-static">{{ employee.birth_date.strftime('%Y-%m-%d') }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>مكان الميلاد</label>
                                    <p class="form-control-static">
                                        {{ employee.birth_commune.name_ar if employee.birth_commune else 'غير محدد' }}, 
                                        {{ employee.birth_wilaya.name_ar if employee.birth_wilaya else 'غير محدد' }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>الحالة العائلية</label>
                                    <p class="form-control-static">{{ employee.marital_status }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>عدد الأبناء</label>
                                    <p class="form-control-static">{{ employee.children_count }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>عدد المتكفل بهم</label>
                                    <p class="form-control-static">{{ employee.dependents_count }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>زمرة الدم</label>
                                    <p class="form-control-static">{{ employee.blood_type or 'غير محدد' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- البيانات المهنية -->
                    <div class="tab-pane" id="professional">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>السلك</label>
                                    <p class="form-control-static">{{ employee.corps.name if employee.corps else 'غير محدد' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>الرتبة الحالية</label>
                                    <p class="form-control-static">{{ employee.current_rank.name if employee.current_rank else 'غير محدد' }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>تاريخ الترقية في الرتبة</label>
                                    <p class="form-control-static">
                                        {{ employee.rank_promotion_date.strftime('%Y-%m-%d') if employee.rank_promotion_date else 'غير محدد' }}
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>الوظيفة الحالية</label>
                                    <p class="form-control-static">{{ employee.current_position.name if employee.current_position else 'غير محدد' }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>المديرية</label>
                                    <p class="form-control-static">{{ employee.directorate.name if employee.directorate else 'غير محدد' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>المصلحة</label>
                                    <p class="form-control-static">{{ employee.service.name if employee.service else 'غير محدد' }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>تاريخ التوظيف</label>
                                    <p class="form-control-static">{{ employee.hiring_date.strftime('%Y-%m-%d') }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>رتبة التوظيف</label>
                                    <p class="form-control-static">{{ employee.hiring_rank.name if employee.hiring_rank else 'غير محدد' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الوثائق -->
                    <div class="tab-pane" id="documents">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>رقم الضمان الاجتماعي</label>
                                    <p class="form-control-static">{{ employee.social_security_number }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>رقم الحساب الجاري البريدي</label>
                                    <p class="form-control-static">{{ employee.postal_account_number or 'غير محدد' }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>رقم بطاقة التعريف الوطنية</label>
                                    <p class="form-control-static">{{ employee.national_id_number }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>تاريخ صدور بطاقة التعريف</label>
                                    <p class="form-control-static">
                                        {{ employee.national_id_issue_date.strftime('%Y-%m-%d') if employee.national_id_issue_date else 'غير محدد' }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>رقم البطاقة المهنية</label>
                                    <p class="form-control-static">{{ employee.professional_card_number or 'غير محدد' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>رقم رخصة السياقة</label>
                                    <p class="form-control-static">{{ employee.driving_license_number or 'غير محدد' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأسرة -->
                    <div class="tab-pane" id="family">
                        <h5>معلومات الزوجة</h5>
                        {% if employee.spouses %}
                            {% for spouse in employee.spouses %}
                                <div class="card">
                                    <div class="card-body">
                                        <p><strong>الاسم:</strong> {{ spouse.first_name }} {{ spouse.last_name }}</p>
                                        <p><strong>تاريخ الميلاد:</strong> {{ spouse.birth_date.strftime('%Y-%m-%d') }}</p>
                                        <p><strong>الوظيفة:</strong> {{ spouse.job or 'غير محدد' }}</p>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">لا توجد معلومات عن الزوجة</p>
                        {% endif %}

                        <h5 class="mt-4">معلومات الأولاد</h5>
                        {% if employee.children %}
                            {% for child in employee.children %}
                                <div class="card">
                                    <div class="card-body">
                                        <p><strong>الاسم:</strong> {{ child.first_name }} {{ child.last_name }}</p>
                                        <p><strong>الجنس:</strong> {{ child.gender }}</p>
                                        <p><strong>متمدرس:</strong> {{ 'نعم' if child.is_student else 'لا' }}</p>
                                        {% if child.is_student %}
                                            <p><strong>المستوى:</strong> {{ child.education_level or 'غير محدد' }}</p>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">لا توجد معلومات عن الأولاد</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="إغلاق">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من أنك تريد حذف بيانات الموظف <strong>{{ employee.full_name }}</strong>؟ 
                هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع البيانات المرتبطة بالموظف.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">حذف نهائياً</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(employeeId) {
    $('#deleteForm').attr('action', '/employee/' + employeeId + '/delete');
    $('#deleteModal').modal('show');
}
</script>
{% endblock %}

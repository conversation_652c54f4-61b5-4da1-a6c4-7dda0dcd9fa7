<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}برنامج تسيير مستخدمي الجمارك الجزائرية{% endblock %}</title>

    <!-- Google Font: Cairo for Arabic -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap 5 RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- AdminLTE 3 (more stable) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2.0/dist/css/adminlte.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom.css') }}">
    <!-- RTL Fix CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/rtl-fix.css') }}">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-sidebar {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        }
        
        .nav-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.9);
        }
        
        .nav-sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .nav-sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .brand-link {
            background-color: rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .brand-text {
            color: white !important;
            font-weight: bold;
        }
        
        .content-wrapper {
            background-color: #f8f9fa;
        }
        
        .card {
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
            border: none;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        
        .navbar-nav .nav-link {
            color: #495057;
        }
        
        .employee-photo {
            width: 150px;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .info-box {
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
            border-radius: 8px;
        }
        
        .info-box-icon {
            border-radius: 8px 0 0 8px;
        }
        
        .alert {
            border: none;
            border-radius: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 6px;
            border: 1px solid #ced4da;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>

<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

    <!-- Preloader -->
    <div class="preloader flex-column justify-content-center align-items-center">
        <div class="animation__shake" style="width: 60px; height: 60px; background: linear-gradient(45deg, #1e3c72, #2a5298); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 24px;">ج</div>
    </div>

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
        <!-- Left navbar links -->
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="{{ url_for('index') }}" class="nav-link">الرئيسية</a>
            </li>
        </ul>

        <!-- Right navbar links -->
        <ul class="navbar-nav mr-auto-navbav">
            <li class="nav-item">
                <a class="nav-link" data-widget="fullscreen" href="#" role="button">
                    <i class="fas fa-expand-arrows-alt"></i>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Sidebar Container -->
    <aside class="main-sidebar sidebar-dark-primary elevation-4">
        <!-- Brand Logo -->
        <a href="{{ url_for('index') }}" class="brand-link">
            <div class="brand-image img-circle elevation-3" style="width: 33px; height: 33px; background: linear-gradient(45deg, #1e3c72, #2a5298); border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 16px; margin-left: 10px; opacity: 0.8;">ج</div>
            <span class="brand-text font-weight-light">الجمارك الجزائرية</span>
        </a>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Sidebar Menu -->
            <nav class="mt-2">
                <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                    
                    <li class="nav-item">
                        <a href="{{ url_for('index') }}" class="nav-link {% if request.endpoint == 'index' %}active{% endif %}">
                            <i class="nav-icon fas fa-tachometer-alt"></i>
                            <p>لوحة التحكم</p>
                        </a>
                    </li>
                    
                    <li class="nav-item {% if request.endpoint and 'employee' in request.endpoint %}menu-open{% endif %}">
                        <a href="#" class="nav-link {% if request.endpoint and 'employee' in request.endpoint %}active{% endif %}">
                            <i class="nav-icon fas fa-users"></i>
                            <p>
                                إدارة الموظفين
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a href="{{ url_for('employees_list') }}" class="nav-link {% if request.endpoint == 'employees_list' %}active{% endif %}">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>قائمة الموظفين</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url_for('add_employee') }}" class="nav-link {% if request.endpoint == 'add_employee' %}active{% endif %}">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>إضافة موظف جديد</p>
                                </a>
                            </li>
                        </ul>
                    </li>
                    
                    <li class="nav-item {% if request.endpoint and 'settings' in request.endpoint %}menu-open{% endif %}">
                        <a href="#" class="nav-link {% if request.endpoint and 'settings' in request.endpoint %}active{% endif %}">
                            <i class="nav-icon fas fa-cogs"></i>
                            <p>
                                الإعدادات
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a href="{{ url_for('administrative_divisions') }}" class="nav-link {% if request.endpoint == 'administrative_divisions' %}active{% endif %}">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>التقسيم الإداري</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url_for('ranks_settings') }}" class="nav-link {% if request.endpoint == 'ranks_settings' %}active{% endif %}">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>الرتب</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url_for('positions_settings') }}" class="nav-link {% if request.endpoint == 'positions_settings' %}active{% endif %}">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>الوظائف</p>
                                </a>
                            </li>
                        </ul>
                    </li>
                    
                </ul>
            </nav>
        </div>
    </aside>

    <!-- Content Wrapper -->
    <div class="content-wrapper">
        <!-- Content Header -->
        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0">{% block page_title %}{% endblock %}</h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-left">
                            {% block breadcrumb %}
                            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                            {% endblock %}
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                {% block content %}{% endblock %}
                
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="main-footer">
        <strong>حقوق الطبع والنشر &copy; 2024 <a href="#">الجمارك الجزائرية</a>.</strong>
        جميع الحقوق محفوظة.
        <div class="float-left d-none d-sm-inline-block">
            <b>الإصدار</b> 1.0.0
        </div>
    </footer>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- Bootstrap 5 -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE 3 (more stable) -->
<script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2.0/dist/js/adminlte.min.js"></script>

<!-- RTL Fix Script -->
<script>
$(document).ready(function() {
    // Force RTL layout
    $('body').addClass('sidebar-mini layout-fixed');

    // Fix sidebar positioning
    $('.main-sidebar').css({
        'right': '0',
        'left': 'auto'
    });

    // Fix content wrapper margin
    $('.content-wrapper, .main-header.navbar').css({
        'margin-right': '250px',
        'margin-left': '0'
    });

    // Fix sidebar toggle functionality
    $('[data-widget="pushmenu"]').on('click', function() {
        setTimeout(function() {
            if ($('body').hasClass('sidebar-collapse')) {
                $('.content-wrapper, .main-header.navbar').css({
                    'margin-right': '4.6rem',
                    'margin-left': '0'
                });
            } else {
                $('.content-wrapper, .main-header.navbar').css({
                    'margin-right': '250px',
                    'margin-left': '0'
                });
            }
        }, 300);
    });

    // Fix dropdown menus
    $('.dropdown-menu').css({
        'right': '0',
        'left': 'auto'
    });

    // Fix breadcrumb direction
    $('.breadcrumb-item + .breadcrumb-item::before').css('content', '"←"');
});
</script>

<!-- Custom App JavaScript -->
<script src="{{ url_for('static', filename='js/customs-app.js') }}"></script>

{% block extra_js %}{% endblock %}

</body>
</html>

# 🇩🇿 نظام إدارة الجمارك الجزائرية المتكامل
## Complete Algerian Customs Management System

نظام شامل ومتطور لإدارة الموارد البشرية في الجمارك الجزائرية مع قاعدة بيانات متكاملة وواجهة احترافية.

---

## 🚀 المميزات الرئيسية

### 📊 **لوحة تحكم متطورة**
- إحصائيات شاملة ومفصلة
- رسوم بيانية تفاعلية
- مؤشرات الأداء الرئيسية
- تتبع الأنشطة الحديثة

### 👥 **إدارة الموظفين المتكاملة**
- قاعدة بيانات شاملة للموظفين
- بحث وتصفية متقدمة
- نماذج إدخال ذكية
- إدارة الملفات والوثائق

### 🏢 **الهيكل التنظيمي**
- إدارة المديريات والمصالح
- تصنيف الأسلاك والرتب
- توزيع الوظائف والمهام
- التقسيم الإداري الكامل

### 🎨 **واجهة احترافية**
- تصميم عصري ومتجاوب
- دعم كامل للغة العربية (RTL)
- ألوان مناسبة للهوية المؤسسية
- تجربة مستخدم سلسة

---

## 🛠️ التقنيات المستخدمة

### Backend
- **Flask 2.3.3** - إطار العمل الرئيسي
- **SQLAlchemy** - إدارة قاعدة البيانات
- **Flask-Migrate** - إدارة تحديثات قاعدة البيانات
- **WTForms** - نماذج الإدخال والتحقق

### Frontend
- **Bootstrap 5 RTL** - إطار العمل للواجهة
- **AdminLTE 3** - قالب لوحة التحكم
- **Font Awesome 6** - الأيقونات
- **jQuery** - التفاعل والديناميكية

### Database
- **SQLite** - قاعدة البيانات (قابلة للتطوير لـ PostgreSQL/MySQL)

---

## 📦 التثبيت والتشغيل

### 1. متطلبات النظام
```bash
Python 3.8+
pip (مدير الحزم)
```

### 2. تحميل المشروع
```bash
git clone [repository-url]
cd customs-management
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements_complete.txt
```

### 4. تشغيل النظام
```bash
python run_complete.py
```

### 5. الوصول للنظام
```
http://localhost:5000
```

---

## 📁 هيكل المشروع

```
customs-management/
├── complete_app.py              # التطبيق الرئيسي
├── run_complete.py              # ملف التشغيل المحسن
├── requirements_complete.txt    # المتطلبات
├── customs_management.db        # قاعدة البيانات
├── static/                      # الملفات الثابتة
│   ├── css/
│   │   └── professional.css     # التصميم الاحترافي
│   ├── js/
│   ├── uploads/                 # ملفات الرفع
│   ├── documents/               # الوثائق
│   └── photos/                  # صور الموظفين
├── templates/                   # القوالب
│   ├── professional_base.html   # القالب الأساسي
│   └── complete/                # قوالب النظام المتكامل
│       ├── dashboard.html       # لوحة التحكم
│       ├── employees_list.html  # قائمة الموظفين
│       ├── employee_form.html   # نموذج الموظف
│       └── employee_detail.html # تفاصيل الموظف
└── migrations/                  # تحديثات قاعدة البيانات
```

---

## 🗄️ قاعدة البيانات

### الجداول الرئيسية

#### 👤 **employees** - الموظفين
- المعلومات الشخصية الكاملة
- بيانات الاتصال
- المعلومات العائلية
- البيانات الرسمية
- معلومات التوظيف

#### 🏢 **directorates** - المديريات
- المديرية العامة
- المديريات الجهوية
- المديريات المتخصصة

#### 🏛️ **services** - المصالح
- مصالح كل مديرية
- التخصصات المختلفة

#### 🎖️ **ranks** - الرتب
- رتب الأسلاك المختلفة
- مستويات الترقية

#### 💼 **positions** - الوظائف
- الوظائف الإدارية
- الوظائف التقنية
- المسؤوليات

#### 🌍 **wilayas & communes** - التقسيم الإداري
- جميع ولايات الجزائر (58 ولاية)
- البلديات التابعة

---

## 🎯 الوظائف المتاحة

### 📊 **لوحة التحكم**
- `/` - الصفحة الرئيسية
- إحصائيات شاملة
- مؤشرات الأداء
- الأنشطة الحديثة

### 👥 **إدارة الموظفين**
- `/employees` - قائمة الموظفين
- `/employee/add` - إضافة موظف جديد
- `/employee/<id>` - تفاصيل الموظف
- `/employee/<id>/edit` - تعديل بيانات الموظف

### 🔍 **البحث والتصفية**
- بحث متقدم بمعايير متعددة
- تصفية حسب الحالة والجنس
- تصفية حسب المديرية والمصلحة
- ترتيب النتائج

### 📄 **التقارير والتصدير**
- تصدير البيانات إلى Excel
- طباعة القوائم
- تقارير مفصلة

---

## 🔧 الإعدادات والتخصيص

### تغيير المنفذ
```python
# في ملف run_complete.py
app.run(debug=True, host='0.0.0.0', port=8000)  # بدلاً من 5000
```

### تغيير قاعدة البيانات
```python
# في ملف complete_app.py
app.config['SQLALCHEMY_DATABASE_URI'] = 'postgresql://user:pass@localhost/customs_db'
```

### إضافة بيانات جديدة
```python
# تشغيل Python shell
python -c "from complete_app import *; init_database()"
```

---

## 🚀 التطوير والتحسين

### إضافة ميزات جديدة
1. إنشاء نماذج جديدة في `complete_app.py`
2. إضافة الروتات المطلوبة
3. إنشاء القوالب في `templates/complete/`
4. تحديث قاعدة البيانات

### تحديث التصميم
- تعديل `static/css/professional.css`
- تخصيص الألوان والخطوط
- إضافة تأثيرات جديدة

### إضافة لغات جديدة
- إنشاء ملفات الترجمة
- تحديث القوالب
- إضافة دعم i18n

---

## 📱 التوافق والاستجابة

### المتصفحات المدعومة
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

### الأجهزة المدعومة
- ✅ أجهزة الكمبيوتر المكتبية
- ✅ أجهزة الكمبيوتر المحمولة
- ✅ الأجهزة اللوحية
- ✅ الهواتف الذكية

---

## 🔒 الأمان والحماية

### الميزات الأمنية
- حماية CSRF
- تشفير كلمات المرور
- التحقق من صحة البيانات
- حماية من SQL Injection

### النسخ الاحتياطية
```bash
# نسخ احتياطي لقاعدة البيانات
cp customs_management.db backup_$(date +%Y%m%d).db
```

---

## 🆘 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### المشكلة: خطأ في تشغيل التطبيق
```bash
# الحل: تأكد من تثبيت المتطلبات
pip install -r requirements_complete.txt
```

#### المشكلة: خطأ في قاعدة البيانات
```bash
# الحل: إعادة تهيئة قاعدة البيانات
rm customs_management.db
python run_complete.py
```

#### المشكلة: مشكلة في الواجهة
```bash
# الحل: مسح ذاكرة التخزين المؤقت
Ctrl + F5 في المتصفح
```

---

## 📞 الدعم والمساعدة

### للحصول على المساعدة
1. راجع هذا الدليل أولاً
2. تحقق من سجلات الأخطاء
3. تأكد من تحديث المتطلبات

### للتطوير المتقدم
- راجع كود المصدر في `complete_app.py`
- ادرس هيكل قاعدة البيانات
- اطلع على القوالب في `templates/`

---

## 🎉 الخلاصة

هذا نظام متكامل وشامل لإدارة الموارد البشرية في الجمارك الجزائرية، مصمم ليكون:

- **احترافي** في التصميم والوظائف
- **سهل الاستخدام** للموظفين والإدارة
- **قابل للتطوير** والتخصيص
- **آمن** وموثوق
- **متوافق** مع جميع الأجهزة

**النظام جاهز للاستخدام الفوري والتطوير المستقبلي! 🚀**

---

**© 2024 نظام إدارة الجمارك الجزائرية - جميع الحقوق محفوظة**

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل برنامج تسيير مستخدمي الجمارك الجزائرية
"""

import os
import sys
from app import app, db

def create_app():
    """إنشاء وتكوين التطبيق"""
    
    # التأكد من وجود مجلد uploads
    upload_folder = app.config['UPLOAD_FOLDER']
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
        print(f"تم إنشاء مجلد الرفع: {upload_folder}")
    
    # تهيئة قاعدة البيانات إذا لم تكن موجودة
    if not os.path.exists('customs_employees.db'):
        print("قاعدة البيانات غير موجودة، سيتم إنشاؤها...")
        with app.app_context():
            db.create_all()
            
            # تهيئة البيانات الأساسية
            try:
                from models import init_default_data
                init_default_data()
                print("تم إنشاء قاعدة البيانات وتهيئة البيانات الأساسية بنجاح!")
            except Exception as e:
                print(f"خطأ في تهيئة البيانات الأساسية: {e}")
    
    return app

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    
    print("=" * 60)
    print("برنامج تسيير مستخدمي الجمارك الجزائرية")
    print("Algerian Customs Employees Management System")
    print("=" * 60)
    print()
    
    # إنشاء التطبيق
    app = create_app()
    
    # معلومات التشغيل
    host = '0.0.0.0'
    port = 5000
    debug = True
    
    print(f"🚀 بدء تشغيل الخادم...")
    print(f"📍 العنوان: http://localhost:{port}")
    print(f"🔧 وضع التطوير: {'مفعل' if debug else 'معطل'}")
    print(f"📁 مجلد الرفع: {app.config['UPLOAD_FOLDER']}")
    print()
    print("للإيقاف اضغط Ctrl+C")
    print("=" * 60)
    
    try:
        # تشغيل التطبيق
        app.run(
            host=host,
            port=port,
            debug=debug,
            use_reloader=True,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("تم إيقاف الخادم بنجاح!")
        print("شكراً لاستخدام برنامج تسيير مستخدمي الجمارك الجزائرية")
        print("=" * 60)
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()

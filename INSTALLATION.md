# دليل التثبيت والتشغيل
## Installation and Setup Guide

## متطلبات النظام

### البرامج المطلوبة:
- Python 3.8 أو أحدث
- pip (مدير حزم Python)
- متصفح ويب حديث (Chrome, Firefox, Edge)

### نظم التشغيل المدعومة:
- Windows 10/11
- macOS 10.15+
- Linux (Ubuntu 18.04+, CentOS 7+)

## خطوات التثبيت

### 1. تحميل المشروع

```bash
# إذا كان لديك Git
git clone [repository-url]
cd customs-employees-management

# أو قم بتحميل الملف المضغوط واستخراجه
```

### 2. إنشاء بيئة افتراضية (مستحسن)

#### على Windows:
```cmd
python -m venv venv
venv\Scripts\activate
```

#### على macOS/Linux:
```bash
python3 -m venv venv
source venv/bin/activate
```

### 3. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 4. تهيئة قاعدة البيانات

#### الطريقة الأولى - تشغيل ملف التهيئة:
```bash
python init_db.py
```

#### الطريقة الثانية - التهيئة التلقائية:
```bash
# سيتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل
python run.py
```

### 5. تشغيل التطبيق

#### التشغيل العادي:
```bash
python run.py
```

#### التشغيل المبسط (للاختبار):
```bash
python simple_app.py
```

### 6. فتح التطبيق

افتح المتصفح وانتقل إلى:
```
http://localhost:5000
```

## استكشاف الأخطاء

### مشكلة: خطأ في استيراد المكتبات

**الحل:**
```bash
# تأكد من تفعيل البيئة الافتراضية
pip install --upgrade pip
pip install -r requirements.txt
```

### مشكلة: خطأ في قاعدة البيانات

**الحل:**
```bash
# احذف قاعدة البيانات وأعد إنشاؤها
rm customs_employees.db
python init_db.py
```

### مشكلة: المنفذ 5000 مستخدم

**الحل:**
```bash
# غير المنفذ في ملف run.py أو simple_app.py
# من port=5000 إلى port=5001
```

### مشكلة: لا تظهر الصور

**الحل:**
```bash
# تأكد من وجود مجلد uploads
mkdir -p static/uploads
chmod 755 static/uploads
```

## إعدادات التطوير

### تفعيل وضع التطوير:
```bash
export FLASK_ENV=development  # Linux/Mac
set FLASK_ENV=development     # Windows
python run.py
```

### تشغيل الاختبارات:
```bash
python test_app.py
python test_simple.py
```

## إعدادات الإنتاج

### 1. تغيير المفتاح السري:
```python
# في ملف config.py
SECRET_KEY = 'your-very-secure-secret-key-here'
```

### 2. استخدام قاعدة بيانات أقوى:
```python
# في ملف config.py
SQLALCHEMY_DATABASE_URI = 'postgresql://user:pass@localhost/customs_db'
```

### 3. تكوين خادم الويب:
```bash
# استخدام Gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 run:app
```

## الملفات المهمة

### ملفات التكوين:
- `config.py` - إعدادات التطبيق
- `requirements.txt` - المكتبات المطلوبة

### ملفات التشغيل:
- `run.py` - ملف التشغيل الرئيسي
- `simple_app.py` - ملف التشغيل المبسط
- `app.py` - التطبيق الأساسي

### ملفات قاعدة البيانات:
- `models.py` - نماذج البيانات
- `init_db.py` - تهيئة قاعدة البيانات
- `customs_employees.db` - ملف قاعدة البيانات

### ملفات الاختبار:
- `test_app.py` - اختبارات شاملة
- `test_simple.py` - اختبارات بسيطة

## نصائح مهمة

### الأمان:
- غير المفتاح السري في الإنتاج
- استخدم HTTPS في الإنتاج
- قم بعمل نسخ احتياطية منتظمة

### الأداء:
- استخدم قاعدة بيانات أقوى في الإنتاج
- فعل ضغط الملفات الثابتة
- استخدم CDN للملفات الكبيرة

### الصيانة:
- راقب سجلات الأخطاء
- قم بتحديث المكتبات بانتظام
- اختبر النسخ الاحتياطية

## الدعم الفني

### في حالة وجود مشاكل:
1. تحقق من سجلات الأخطاء
2. تأكد من تثبيت جميع المتطلبات
3. راجع هذا الدليل
4. ابحث في الوثائق
5. اتصل بفريق الدعم

### معلومات مفيدة:
- إصدار Python: `python --version`
- إصدار pip: `pip --version`
- قائمة المكتبات: `pip list`
- معلومات النظام: `python -m platform`

---

**ملاحظة:** هذا الدليل يغطي التثبيت الأساسي. للإعدادات المتقدمة، راجع الوثائق التفصيلية.

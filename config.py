#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إعدادات برنامج تسيير مستخدمي الجمارك الجزائرية
"""

import os
from datetime import timedelta

class Config:
    """الإعدادات الأساسية للتطبيق"""
    
    # إعدادات Flask الأساسية
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-very-secret-key-change-in-production'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///customs_employees.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = True
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # إعدادات رفع الملفات
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    
    # إعدادات الأمان
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # ساعة واحدة
    
    # إعدادات الجلسة
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    SESSION_COOKIE_SECURE = False  # True في الإنتاج مع HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # إعدادات التطبيق
    ITEMS_PER_PAGE = 20
    LANGUAGES = ['ar', 'fr', 'en']
    DEFAULT_LANGUAGE = 'ar'
    
    # إعدادات البريد الإلكتروني (للمستقبل)
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # إعدادات التسجيل
    LOG_TO_STDOUT = os.environ.get('LOG_TO_STDOUT')
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق مع الإعدادات"""
        pass

class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    DEBUG = True
    TESTING = False
    
    # إعدادات قاعدة البيانات للتطوير
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or 'sqlite:///customs_employees_dev.db'
    
    # إعدادات التسجيل للتطوير
    SQLALCHEMY_ECHO = True  # طباعة استعلامات SQL

class TestingConfig(Config):
    """إعدادات بيئة الاختبار"""
    TESTING = True
    DEBUG = True
    
    # قاعدة بيانات في الذاكرة للاختبارات
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
    # تعطيل CSRF للاختبارات
    WTF_CSRF_ENABLED = False
    
    # مجلد رفع مؤقت للاختبارات
    UPLOAD_FOLDER = '/tmp/test_uploads'

class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    DEBUG = False
    TESTING = False
    
    # إعدادات الأمان للإنتاج
    SESSION_COOKIE_SECURE = True
    WTF_CSRF_SSL_STRICT = True
    
    # إعدادات قاعدة البيانات للإنتاج
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///customs_employees.db'
    SQLALCHEMY_ECHO = False
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # تسجيل الأخطاء عبر البريد الإلكتروني
        import logging
        from logging.handlers import SMTPHandler
        if app.config['MAIL_SERVER']:
            auth = None
            if app.config['MAIL_USERNAME'] or app.config['MAIL_PASSWORD']:
                auth = (app.config['MAIL_USERNAME'], app.config['MAIL_PASSWORD'])
            secure = None
            if app.config['MAIL_USE_TLS']:
                secure = ()
            mail_handler = SMTPHandler(
                mailhost=(app.config['MAIL_SERVER'], app.config['MAIL_PORT']),
                fromaddr='no-reply@' + app.config['MAIL_SERVER'],
                toaddrs=app.config['ADMINS'],
                subject='خطأ في برنامج تسيير مستخدمي الجمارك',
                credentials=auth,
                secure=secure
            )
            mail_handler.setLevel(logging.ERROR)
            app.logger.addHandler(mail_handler)

class DockerConfig(ProductionConfig):
    """إعدادات بيئة Docker"""
    
    @classmethod
    def init_app(cls, app):
        ProductionConfig.init_app(app)
        
        # تسجيل إلى stdout في Docker
        import logging
        from logging import StreamHandler
        file_handler = StreamHandler()
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)

# قاموس الإعدادات
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'docker': DockerConfig,
    'default': DevelopmentConfig
}

# إعدادات خاصة بالجمارك الجزائرية
CUSTOMS_CONFIG = {
    # إعدادات الولايات والبلديات
    'WILAYAS_COUNT': 58,
    'DEFAULT_WILAYA': 'الجزائر',
    
    # إعدادات الأسلاك والرتب
    'CORPS_TYPES': ['خاص', 'مشترك', 'مهني'],
    'MAX_RANK_LEVEL': 15,
    'MIN_RANK_LEVEL': 1,
    
    # إعدادات الموظفين
    'MIN_AGE': 19,
    'MAX_AGE': 65,
    'RETIREMENT_AGE': 60,
    'REGISTRATION_NUMBER_LENGTH': 6,
    
    # إعدادات الوثائق
    'SOCIAL_SECURITY_LENGTH': 15,
    'NATIONAL_ID_LENGTH': 18,
    'POSTAL_ACCOUNT_LENGTH': 10,
    
    # إعدادات العطل
    'ANNUAL_LEAVE_DAYS': 50,
    'MAX_SICK_LEAVE_DAYS': 180,
    'MAX_EXCEPTIONAL_LEAVE_DAYS': 10,
    
    # إعدادات الاستيداع
    'MAX_DEPOSIT_YEARS': 5,
    'MAX_DEPOSIT_PERIODS': 2,
    
    # إعدادات الملفات
    'PHOTO_MAX_SIZE': 5 * 1024 * 1024,  # 5MB
    'PHOTO_ALLOWED_FORMATS': ['jpg', 'jpeg', 'png', 'gif'],
    'DOCUMENT_MAX_SIZE': 10 * 1024 * 1024,  # 10MB
    'DOCUMENT_ALLOWED_FORMATS': ['pdf', 'doc', 'docx'],
    
    # إعدادات التقارير
    'REPORT_FORMATS': ['pdf', 'excel', 'csv'],
    'DEFAULT_REPORT_FORMAT': 'pdf',
    
    # إعدادات النسخ الاحتياطي
    'BACKUP_RETENTION_DAYS': 30,
    'AUTO_BACKUP_ENABLED': True,
    'BACKUP_SCHEDULE': '0 2 * * *',  # يومياً في الساعة 2 صباحاً
}

def get_config():
    """الحصول على إعدادات التطبيق حسب البيئة"""
    config_name = os.environ.get('FLASK_ENV', 'development')
    return config.get(config_name, config['default'])

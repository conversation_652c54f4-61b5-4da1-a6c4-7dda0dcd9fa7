{% extends "base.html" %}

{% block title %}التقسيم الإداري - الإعدادات{% endblock %}

{% block page_title %}التقسيم الإداري{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item"><a href="{{ url_for('settings') }}">الإعدادات</a></li>
<li class="breadcrumb-item active">التقسيم الإداري</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-map-marked-alt"></i> إدارة الولايات والبلديات
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#addWilayaModal">
                        <i class="fas fa-plus"></i> إضافة ولاية
                    </button>
                    <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#importModal">
                        <i class="fas fa-file-import"></i> استيراد من Excel
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Search and Filter -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" id="searchInput" class="form-control" placeholder="البحث في الولايات والبلديات...">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="float-right">
                            <button class="btn btn-info btn-sm" onclick="expandAll()">
                                <i class="fas fa-expand-arrows-alt"></i> توسيع الكل
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="collapseAll()">
                                <i class="fas fa-compress-arrows-alt"></i> طي الكل
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Wilayas List -->
                <div class="accordion" id="wilayasAccordion">
                    {% for wilaya in wilayas %}
                    <div class="card">
                        <div class="card-header" id="heading{{ wilaya.id }}">
                            <h2 class="mb-0">
                                <button class="btn btn-link btn-block text-right collapsed" type="button" 
                                        data-toggle="collapse" data-target="#collapse{{ wilaya.id }}" 
                                        aria-expanded="false" aria-controls="collapse{{ wilaya.id }}">
                                    <div class="row">
                                        <div class="col-md-2">
                                            <span class="badge badge-primary">{{ wilaya.code }}</span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>{{ wilaya.name_ar }}</strong>
                                        </div>
                                        <div class="col-md-4">
                                            <small class="text-muted">{{ wilaya.name_fr }}</small>
                                        </div>
                                        <div class="col-md-2">
                                            <span class="badge badge-info">{{ wilaya.communes|length }} بلدية</span>
                                        </div>
                                    </div>
                                </button>
                            </h2>
                        </div>
                        <div id="collapse{{ wilaya.id }}" class="collapse" aria-labelledby="heading{{ wilaya.id }}" 
                             data-parent="#wilayasAccordion">
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <h5>بلديات ولاية {{ wilaya.name_ar }}</h5>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="float-right">
                                            <button type="button" class="btn btn-success btn-sm" 
                                                    onclick="addCommune({{ wilaya.id }})">
                                                <i class="fas fa-plus"></i> إضافة بلدية
                                            </button>
                                            <button type="button" class="btn btn-warning btn-sm" 
                                                    onclick="editWilaya({{ wilaya.id }})">
                                                <i class="fas fa-edit"></i> تعديل الولاية
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                {% if wilaya.communes %}
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>الرمز</th>
                                                <th>الاسم بالعربية</th>
                                                <th>الاسم بالفرنسية</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for commune in wilaya.communes %}
                                            <tr>
                                                <td><span class="badge badge-secondary">{{ commune.code }}</span></td>
                                                <td>{{ commune.name_ar }}</td>
                                                <td><small class="text-muted">{{ commune.name_fr }}</small></td>
                                                <td>
                                                    <button type="button" class="btn btn-warning btn-xs" 
                                                            onclick="editCommune({{ commune.id }})">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-danger btn-xs" 
                                                            onclick="deleteCommune({{ commune.id }})">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> لا توجد بلديات مسجلة لهذه الولاية.
                                    <button type="button" class="btn btn-success btn-sm float-right" 
                                            onclick="addCommune({{ wilaya.id }})">
                                        <i class="fas fa-plus"></i> إضافة أول بلدية
                                    </button>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Wilaya Modal -->
<div class="modal fade" id="addWilayaModal" tabindex="-1" role="dialog" aria-labelledby="addWilayaModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addWilayaModalLabel">إضافة ولاية جديدة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="إغلاق">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addWilayaForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="wilayaCode">رمز الولاية</label>
                        <input type="text" class="form-control" id="wilayaCode" name="code" 
                               placeholder="مثال: 59" maxlength="2" required>
                        <small class="form-text text-muted">رمز مكون من رقمين</small>
                    </div>
                    <div class="form-group">
                        <label for="wilayaNameAr">الاسم بالعربية</label>
                        <input type="text" class="form-control" id="wilayaNameAr" name="name_ar" 
                               placeholder="مثال: الجزائر" required>
                    </div>
                    <div class="form-group">
                        <label for="wilayaNameFr">الاسم بالفرنسية</label>
                        <input type="text" class="form-control" id="wilayaNameFr" name="name_fr" 
                               placeholder="مثال: Alger">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Commune Modal -->
<div class="modal fade" id="addCommuneModal" tabindex="-1" role="dialog" aria-labelledby="addCommuneModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCommuneModalLabel">إضافة بلدية جديدة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="إغلاق">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addCommuneForm">
                <div class="modal-body">
                    <input type="hidden" id="communeWilayaId" name="wilaya_id">
                    <div class="form-group">
                        <label for="communeCode">رمز البلدية</label>
                        <input type="text" class="form-control" id="communeCode" name="code" 
                               placeholder="مثال: 160101" maxlength="6" required>
                        <small class="form-text text-muted">رمز مكون من 6 أرقام</small>
                    </div>
                    <div class="form-group">
                        <label for="communeNameAr">الاسم بالعربية</label>
                        <input type="text" class="form-control" id="communeNameAr" name="name_ar" 
                               placeholder="مثال: الجزائر الوسطى" required>
                    </div>
                    <div class="form-group">
                        <label for="communeNameFr">الاسم بالفرنسية</label>
                        <input type="text" class="form-control" id="communeNameFr" name="name_fr" 
                               placeholder="مثال: Alger Centre">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1" role="dialog" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">استيراد التقسيم الإداري من Excel</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="إغلاق">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> تعليمات الاستيراد:</h6>
                    <ul>
                        <li>يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)</li>
                        <li>يجب أن تحتوي الورقة الأولى على بيانات الولايات</li>
                        <li>يجب أن تحتوي الورقة الثانية على بيانات البلديات</li>
                        <li>الأعمدة المطلوبة: الرمز، الاسم بالعربية، الاسم بالفرنسية</li>
                    </ul>
                </div>
                
                <form id="importForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="importFile">اختر ملف Excel</label>
                        <input type="file" class="form-control-file" id="importFile" name="file" 
                               accept=".xlsx,.xls" required>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="replaceExisting" name="replace">
                        <label class="form-check-label" for="replaceExisting">
                            استبدال البيانات الموجودة
                        </label>
                        <small class="form-text text-muted">
                            تحذير: سيتم حذف جميع البيانات الموجودة واستبدالها بالبيانات الجديدة
                        </small>
                    </div>
                </form>
                
                <div id="importProgress" class="mt-3" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted">جاري الاستيراد...</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="startImport()">
                    <i class="fas fa-upload"></i> بدء الاستيراد
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Search functionality
$('#searchInput').on('keyup', function() {
    var value = $(this).val().toLowerCase();
    $('.card').filter(function() {
        $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
    });
});

// Expand/Collapse functions
function expandAll() {
    $('.collapse').collapse('show');
}

function collapseAll() {
    $('.collapse').collapse('hide');
}

// Add commune function
function addCommune(wilayaId) {
    $('#communeWilayaId').val(wilayaId);
    $('#addCommuneModal').modal('show');
}

// Edit functions (placeholder)
function editWilaya(wilayaId) {
    alert('تعديل الولاية رقم: ' + wilayaId);
}

function editCommune(communeId) {
    alert('تعديل البلدية رقم: ' + communeId);
}

function deleteCommune(communeId) {
    if (confirm('هل أنت متأكد من حذف هذه البلدية؟')) {
        alert('حذف البلدية رقم: ' + communeId);
    }
}

// Import function
function startImport() {
    var fileInput = document.getElementById('importFile');
    if (!fileInput.files.length) {
        alert('يرجى اختيار ملف للاستيراد');
        return;
    }
    
    $('#importProgress').show();
    // Simulate import progress
    var progress = 0;
    var interval = setInterval(function() {
        progress += 10;
        $('.progress-bar').css('width', progress + '%');
        if (progress >= 100) {
            clearInterval(interval);
            setTimeout(function() {
                $('#importModal').modal('hide');
                $('#importProgress').hide();
                $('.progress-bar').css('width', '0%');
                alert('تم الاستيراد بنجاح!');
                location.reload();
            }, 500);
        }
    }, 200);
}

// Form submissions
$('#addWilayaForm').on('submit', function(e) {
    e.preventDefault();
    // Add AJAX call here
    alert('تم إضافة الولاية بنجاح!');
    $('#addWilayaModal').modal('hide');
    location.reload();
});

$('#addCommuneForm').on('submit', function(e) {
    e.preventDefault();
    // Add AJAX call here
    alert('تم إضافة البلدية بنجاح!');
    $('#addCommuneModal').modal('hide');
    location.reload();
});
</script>
{% endblock %}

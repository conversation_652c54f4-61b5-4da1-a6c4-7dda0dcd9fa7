{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة الجمارك الجزائرية{% endblock %}

{% block page_title %}لوحة التحكم{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item active">لوحة التحكم</li>
{% endblock %}

{% block content %}

<!-- Page Header -->
<div class="page-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-tachometer-alt ml-2"></i>
                    لوحة التحكم
                </h1>
            </div>
            <div class="col-md-6 text-left">
                <span class="text-muted">آخر تحديث: {{ moment().format('DD/MM/YYYY - HH:mm') }}</span>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Row -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card primary">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">إجمالي الموظفين</div>
                    <div class="stats-number">{{ total_employees or 150 }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-users stats-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card success">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">الموظفين النشطين</div>
                    <div class="stats-number">{{ active_employees or 140 }}</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-user-check stats-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card warning">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">في الإجازة</div>
                    <div class="stats-number">8</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-calendar-times stats-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card danger">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="stats-label">التقارير المعلقة</div>
                    <div class="stats-number">3</div>
                </div>
                <div class="col-auto">
                    <i class="fas fa-exclamation-triangle stats-icon"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt ml-2"></i>
                الإجراءات السريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('add_employee') }}" class="quick-action">
                            <div class="quick-action-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <h5 class="quick-action-title">إضافة موظف جديد</h5>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('employees_list') }}" class="quick-action">
                            <div class="quick-action-icon">
                                <i class="fas fa-list"></i>
                            </div>
                            <h5 class="quick-action-title">قائمة الموظفين</h5>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('settings') }}" class="quick-action">
                            <div class="quick-action-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <h5 class="quick-action-title">الإعدادات</h5>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="quick-action">
                            <div class="quick-action-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h5 class="quick-action-title">التقارير</h5>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Row -->
<div class="row">
    <!-- Recent Activities -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-clock ml-2"></i>
                الأنشطة الأخيرة
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>النشاط</th>
                                <th>المستخدم</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>إضافة موظف جديد</td>
                                <td>أحمد محمد</td>
                                <td>اليوم 14:30</td>
                                <td><span class="badge badge-success">مكتمل</span></td>
                            </tr>
                            <tr>
                                <td>تحديث بيانات موظف</td>
                                <td>فاطمة علي</td>
                                <td>اليوم 12:15</td>
                                <td><span class="badge badge-success">مكتمل</span></td>
                            </tr>
                            <tr>
                                <td>طلب إجازة</td>
                                <td>محمد حسن</td>
                                <td>أمس 16:45</td>
                                <td><span class="badge badge-warning">معلق</span></td>
                            </tr>
                            <tr>
                                <td>تقرير شهري</td>
                                <td>سارة أحمد</td>
                                <td>أمس 10:20</td>
                                <td><span class="badge badge-primary">قيد المراجعة</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie ml-2"></i>
                إحصائيات سريعة
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h6 class="text-primary">معدل الحضور</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-primary" style="width: 95%"></div>
                    </div>
                    <small class="text-muted">95% هذا الشهر</small>
                </div>

                <div class="mb-4">
                    <h6 class="text-success">الأداء العام</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-success" style="width: 88%"></div>
                    </div>
                    <small class="text-muted">88% تقييم ممتاز</small>
                </div>

                <div class="mb-4">
                    <h6 class="text-warning">المهام المكتملة</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-warning" style="width: 72%"></div>
                    </div>
                    <small class="text-muted">72% من المهام</small>
                </div>

                <div class="text-center mt-4">
                    <a href="#" class="btn btn-primary btn-sm">
                        <i class="fas fa-chart-line ml-1"></i>
                        عرض التقرير الكامل
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notifications -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bell ml-2"></i>
                الإشعارات والتنبيهات
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle ml-2"></i>
                            <strong>تذكير:</strong> اجتماع الإدارة غداً الساعة 10:00 صباحاً
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle ml-2"></i>
                            <strong>تنبيه:</strong> 3 طلبات إجازة تحتاج للموافقة
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle ml-2"></i>
                            <strong>مكتمل:</strong> تم رفع النسخة الاحتياطية بنجاح
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add hover effects to stats cards
    $('.stats-card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );
    
    // Add click animation to quick actions
    $('.quick-action').click(function() {
        $(this).addClass('animate__animated animate__pulse');
        setTimeout(() => {
            $(this).removeClass('animate__animated animate__pulse');
        }, 1000);
    });
});
</script>
{% endblock %}

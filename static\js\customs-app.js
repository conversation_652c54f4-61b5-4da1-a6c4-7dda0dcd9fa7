/**
 * JavaScript للتطبيق الجمركي
 * Customs Application JavaScript
 */

$(document).ready(function() {
    
    // تهيئة التطبيق
    initializeApp();
    
    // تهيئة الجداول
    initializeTables();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة الأزرار
    initializeButtons();
    
});

/**
 * تهيئة التطبيق الأساسية
 */
function initializeApp() {
    // إضافة فئات CSS للجسم
    $('body').addClass('sidebar-mini layout-fixed');
    
    // تحسين RTL
    fixRTLLayout();
    
    // تحسين الألوان
    enhanceColors();
    
    // إضافة تأثيرات التحويم
    addHoverEffects();
}

/**
 * إصلاح تخطيط RTL
 */
function fixRTLLayout() {
    // إصلاح الشريط الجانبي
    $('.main-sidebar').css({
        'right': '0',
        'left': 'auto'
    });
    
    // إصلاح هامش المحتوى
    $('.content-wrapper, .main-header.navbar').css({
        'margin-right': '250px',
        'margin-left': '0'
    });
    
    // إصلاح القوائم المنسدلة
    $('.dropdown-menu').css({
        'right': '0',
        'left': 'auto'
    });
    
    // إصلاح أدوات البطاقة
    $('.card-tools').css('float', 'left');
    
    // إصلاح الأيقونات في القائمة
    $('.nav-sidebar .nav-icon').css({
        'float': 'right',
        'margin-left': '0.5rem',
        'margin-right': '0'
    });
}

/**
 * تحسين الألوان
 */
function enhanceColors() {
    // ألوان الجمارك المخصصة
    const customsColors = {
        primary: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)',
        secondary: 'linear-gradient(135deg, #134e5e 0%, #71b280 100%)',
        accent: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)'
    };
    
    // تطبيق الألوان على العناصر
    $('.customs-primary').css('background', customsColors.primary);
    $('.customs-secondary').css('background', customsColors.secondary);
    $('.customs-accent').css('background', customsColors.accent);
}

/**
 * إضافة تأثيرات التحويم
 */
function addHoverEffects() {
    // تأثير التحويم على البطاقات
    $('.card').hover(
        function() {
            $(this).css({
                'transform': 'translateY(-2px)',
                'box-shadow': '0 8px 25px rgba(0,0,0,0.15)'
            });
        },
        function() {
            $(this).css({
                'transform': 'translateY(0)',
                'box-shadow': '0 4px 6px rgba(0,0,0,0.1)'
            });
        }
    );
    
    // تأثير التحويم على الصناديق الصغيرة
    $('.small-box, .info-box').hover(
        function() {
            $(this).css('transform', 'translateY(-5px)');
        },
        function() {
            $(this).css('transform', 'translateY(0)');
        }
    );
    
    // تأثير التحويم على الأزرار
    $('.btn').hover(
        function() {
            $(this).css('transform', 'scale(1.05)');
        },
        function() {
            $(this).css('transform', 'scale(1)');
        }
    );
}

/**
 * تهيئة الجداول
 */
function initializeTables() {
    // تحسين الجداول
    $('.table').each(function() {
        $(this).addClass('table-hover');
        
        // إضافة فئة للصفوف الزوجية
        $(this).find('tbody tr:even').addClass('table-row-even');
        $(this).find('tbody tr:odd').addClass('table-row-odd');
    });
    
    // تحسين رؤوس الجداول
    $('.table thead th').css({
        'background': 'linear-gradient(45deg, #343a40, #495057)',
        'color': 'white',
        'font-weight': '600',
        'text-align': 'center'
    });
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
    // تحسين حقول الإدخال
    $('.form-control').on('focus', function() {
        $(this).css({
            'border-color': '#007bff',
            'box-shadow': '0 0 0 0.2rem rgba(0,123,255,.25)'
        });
    }).on('blur', function() {
        $(this).css({
            'border-color': '#ced4da',
            'box-shadow': 'none'
        });
    });
    
    // تحقق من صحة النماذج
    $('form').on('submit', function(e) {
        var isValid = true;
        
        // فحص الحقول المطلوبة
        $(this).find('[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid').addClass('is-valid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        }
    });
}

/**
 * تهيئة الأزرار
 */
function initializeButtons() {
    // أزرار الحذف
    $('.btn-delete, .btn-danger[onclick*="delete"]').on('click', function(e) {
        e.preventDefault();
        
        const confirmMessage = 'هل أنت متأكد من أنك تريد حذف هذا العنصر؟';
        
        if (confirm(confirmMessage)) {
            // تنفيذ الحذف
            const href = $(this).attr('href') || $(this).attr('onclick');
            if (href) {
                if ($(this).attr('href')) {
                    window.location.href = href;
                } else {
                    eval($(this).attr('onclick'));
                }
            }
        }
    });
    
    // أزرار التحميل
    $('.btn-submit').on('click', function() {
        const btn = $(this);
        const originalText = btn.text();
        
        btn.prop('disabled', true)
           .html('<span class="loading"></span> جاري المعالجة...');
        
        // إعادة تفعيل الزر بعد 3 ثوان
        setTimeout(function() {
            btn.prop('disabled', false).text(originalText);
        }, 3000);
    });
}

/**
 * عرض تنبيه
 */
function showAlert(message, type = 'info') {
    const alertClass = `alert-${type}`;
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.content').prepend(alertHtml);
    
    // إخفاء التنبيه تلقائياً بعد 5 ثوان
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

/**
 * تحديث الإحصائيات
 */
function updateStatistics() {
    // محاكاة تحديث الإحصائيات
    $('.info-box-number, .small-box h3').each(function() {
        const element = $(this);
        const currentValue = parseInt(element.text()) || 0;
        const newValue = currentValue + Math.floor(Math.random() * 5);
        
        // تأثير العد التصاعدي
        $({count: currentValue}).animate({count: newValue}, {
            duration: 1000,
            step: function() {
                element.text(Math.floor(this.count));
            }
        });
    });
}

/**
 * تصدير البيانات
 */
function exportData(format = 'excel') {
    showAlert(`جاري تصدير البيانات بصيغة ${format}...`, 'info');
    
    // محاكاة عملية التصدير
    setTimeout(function() {
        showAlert('تم تصدير البيانات بنجاح!', 'success');
    }, 2000);
}

/**
 * طباعة الصفحة
 */
function printPage() {
    window.print();
}

/**
 * تحديث الصفحة
 */
function refreshPage() {
    location.reload();
}

/**
 * البحث في الجدول
 */
function searchTable(searchTerm, tableId) {
    const table = $(`#${tableId}`);
    const rows = table.find('tbody tr');
    
    if (!searchTerm) {
        rows.show();
        return;
    }
    
    rows.each(function() {
        const row = $(this);
        const text = row.text().toLowerCase();
        
        if (text.includes(searchTerm.toLowerCase())) {
            row.show();
        } else {
            row.hide();
        }
    });
}

/**
 * فلترة الجدول
 */
function filterTable(filterValue, columnIndex, tableId) {
    const table = $(`#${tableId}`);
    const rows = table.find('tbody tr');
    
    if (!filterValue) {
        rows.show();
        return;
    }
    
    rows.each(function() {
        const row = $(this);
        const cellText = row.find(`td:eq(${columnIndex})`).text();
        
        if (cellText === filterValue) {
            row.show();
        } else {
            row.hide();
        }
    });
}

// تصدير الدوال للاستخدام العام
window.showAlert = showAlert;
window.updateStatistics = updateStatistics;
window.exportData = exportData;
window.printPage = printPage;
window.refreshPage = refreshPage;
window.searchTable = searchTable;
window.filterTable = filterTable;

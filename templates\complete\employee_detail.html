{% extends "professional_base.html" %}

{% block title %}{{ employee.full_name }} - تفاصيل الموظف{% endblock %}

{% block page_title %}تفاصيل الموظف{% endblock %}

{% block breadcrumb %}
{{ super() }}
<li class="breadcrumb-item"><a href="{{ url_for('employees_list') }}">قائمة الموظفين</a></li>
<li class="breadcrumb-item active">{{ employee.full_name }}</li>
{% endblock %}

{% block content %}

<!-- Employee Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card" style="background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%); color: white;">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        {% if employee.photo %}
                            <img src="{{ url_for('static', filename='photos/' + employee.photo) }}" 
                                 class="rounded-circle" width="100" height="100" alt="صورة الموظف">
                        {% else %}
                            <div class="bg-white text-primary rounded-circle d-inline-flex align-items-center justify-content-center" 
                                 style="width: 100px; height: 100px; font-weight: bold; font-size: 36px;">
                                {{ employee.first_name[0] }}{{ employee.last_name[0] }}
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-md-7">
                        <h2 class="mb-1">{{ employee.full_name }}</h2>
                        {% if employee.full_name_fr != employee.full_name %}
                        <h5 class="mb-2 opacity-75">{{ employee.full_name_fr }}</h5>
                        {% endif %}
                        <p class="mb-1">
                            <i class="fas fa-id-badge mr-2"></i>
                            رقم التسجيل: <strong>{{ employee.registration_number }}</strong>
                        </p>
                        <p class="mb-1">
                            <i class="fas fa-briefcase mr-2"></i>
                            {{ employee.current_position.name }} - {{ employee.directorate.name }}
                        </p>
                        <p class="mb-0">
                            <i class="fas fa-medal mr-2"></i>
                            {{ employee.current_rank.name }}
                        </p>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="mb-2">
                            {% if employee.status == 'نشط' %}
                                <span class="badge badge-success badge-lg">{{ employee.status }}</span>
                            {% elif employee.status == 'في إجازة' %}
                                <span class="badge badge-warning badge-lg">{{ employee.status }}</span>
                            {% else %}
                                <span class="badge badge-secondary badge-lg">{{ employee.status }}</span>
                            {% endif %}
                        </div>
                        <div class="btn-group">
                            <a href="{{ url_for('edit_employee', id=employee.id) }}" class="btn btn-light btn-sm">
                                <i class="fas fa-edit mr-1"></i>
                                تعديل
                            </a>
                            <button class="btn btn-outline-light btn-sm" onclick="printProfile()">
                                <i class="fas fa-print mr-1"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Employee Details -->
<div class="row">
    <!-- Personal Information -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user mr-2"></i>
                    المعلومات الشخصية
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>الاسم الكامل:</strong></td>
                        <td>{{ employee.full_name }}</td>
                    </tr>
                    {% if employee.full_name_fr != employee.full_name %}
                    <tr>
                        <td><strong>الاسم بالفرنسية:</strong></td>
                        <td>{{ employee.full_name_fr }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>الجنس:</strong></td>
                        <td>
                            <i class="fas fa-{{ 'mars' if employee.gender == 'ذكر' else 'venus' }} 
                               text-{{ 'primary' if employee.gender == 'ذكر' else 'danger' }} mr-1"></i>
                            {{ employee.gender }}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الميلاد:</strong></td>
                        <td>{{ employee.birth_date.strftime('%d/%m/%Y') if employee.birth_date }}</td>
                    </tr>
                    <tr>
                        <td><strong>العمر:</strong></td>
                        <td>{{ employee.age }} سنة</td>
                    </tr>
                    {% if employee.birth_place %}
                    <tr>
                        <td><strong>مكان الميلاد:</strong></td>
                        <td>{{ employee.birth_place }}</td>
                    </tr>
                    {% endif %}
                    {% if employee.birth_wilaya %}
                    <tr>
                        <td><strong>ولاية الميلاد:</strong></td>
                        <td>{{ employee.birth_wilaya.name_ar }}</td>
                    </tr>
                    {% endif %}
                    {% if employee.blood_type %}
                    <tr>
                        <td><strong>فصيلة الدم:</strong></td>
                        <td><span class="badge badge-info">{{ employee.blood_type }}</span></td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>

    <!-- Contact Information -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-address-book mr-2"></i>
                    معلومات الاتصال
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    {% if employee.phone1 %}
                    <tr>
                        <td><strong>الهاتف الأول:</strong></td>
                        <td>
                            <a href="tel:{{ employee.phone1 }}" class="text-decoration-none">
                                <i class="fas fa-phone mr-1"></i>
                                {{ employee.phone1 }}
                            </a>
                        </td>
                    </tr>
                    {% endif %}
                    {% if employee.phone2 %}
                    <tr>
                        <td><strong>الهاتف الثاني:</strong></td>
                        <td>
                            <a href="tel:{{ employee.phone2 }}" class="text-decoration-none">
                                <i class="fas fa-phone mr-1"></i>
                                {{ employee.phone2 }}
                            </a>
                        </td>
                    </tr>
                    {% endif %}
                    {% if employee.email %}
                    <tr>
                        <td><strong>البريد الإلكتروني:</strong></td>
                        <td>
                            <a href="mailto:{{ employee.email }}" class="text-decoration-none">
                                <i class="fas fa-envelope mr-1"></i>
                                {{ employee.email }}
                            </a>
                        </td>
                    </tr>
                    {% endif %}
                    {% if employee.primary_address %}
                    <tr>
                        <td><strong>العنوان الأساسي:</strong></td>
                        <td>{{ employee.primary_address }}</td>
                    </tr>
                    {% endif %}
                    {% if employee.secondary_address %}
                    <tr>
                        <td><strong>العنوان الثانوي:</strong></td>
                        <td>{{ employee.secondary_address }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>

    <!-- Employment Information -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-briefcase mr-2"></i>
                    معلومات التوظيف
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>تاريخ التوظيف:</strong></td>
                        <td>{{ employee.hiring_date.strftime('%d/%m/%Y') if employee.hiring_date }}</td>
                    </tr>
                    <tr>
                        <td><strong>السلك:</strong></td>
                        <td>{{ employee.corps.name }}</td>
                    </tr>
                    <tr>
                        <td><strong>الرتبة:</strong></td>
                        <td>
                            <span class="badge badge-primary">{{ employee.current_rank.name }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الوظيفة:</strong></td>
                        <td>{{ employee.current_position.name }}</td>
                    </tr>
                    <tr>
                        <td><strong>المديرية:</strong></td>
                        <td>{{ employee.directorate.name }}</td>
                    </tr>
                    {% if employee.service %}
                    <tr>
                        <td><strong>المصلحة:</strong></td>
                        <td>{{ employee.service.name }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>الحالة:</strong></td>
                        <td>
                            {% if employee.status == 'نشط' %}
                                <span class="badge badge-success">{{ employee.status }}</span>
                            {% elif employee.status == 'في إجازة' %}
                                <span class="badge badge-warning">{{ employee.status }}</span>
                            {% else %}
                                <span class="badge badge-secondary">{{ employee.status }}</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Family Information -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-home mr-2"></i>
                    المعلومات العائلية
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    {% if employee.marital_status %}
                    <tr>
                        <td><strong>الحالة المدنية:</strong></td>
                        <td>{{ employee.marital_status }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>عدد الأطفال:</strong></td>
                        <td>
                            <span class="badge badge-info">{{ employee.children_count or 0 }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>عدد المعالين:</strong></td>
                        <td>
                            <span class="badge badge-info">{{ employee.dependents_count or 0 }}</span>
                        </td>
                    </tr>
                </table>

                <!-- Spouses -->
                {% if employee.spouses %}
                <h6 class="mt-3 mb-2">الأزواج:</h6>
                {% for spouse in employee.spouses %}
                <div class="border rounded p-2 mb-2">
                    <strong>{{ spouse.first_name }} {{ spouse.last_name }}</strong>
                    {% if spouse.profession %}
                    <br><small class="text-muted">{{ spouse.profession }}</small>
                    {% endif %}
                </div>
                {% endfor %}
                {% endif %}

                <!-- Children -->
                {% if employee.children %}
                <h6 class="mt-3 mb-2">الأطفال:</h6>
                {% for child in employee.children %}
                <div class="border rounded p-2 mb-2">
                    <strong>{{ child.first_name }} {{ child.last_name }}</strong>
                    <br><small class="text-muted">{{ child.gender }} - {{ child.age }} سنة</small>
                </div>
                {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Official Documents -->
    <div class="col-lg-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-id-card mr-2"></i>
                    الوثائق الرسمية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if employee.social_security_number %}
                    <div class="col-md-6">
                        <strong>رقم الضمان الاجتماعي:</strong>
                        <span class="badge badge-secondary">{{ employee.social_security_number }}</span>
                    </div>
                    {% endif %}
                    {% if employee.national_id_number %}
                    <div class="col-md-6">
                        <strong>رقم البطاقة الوطنية:</strong>
                        <span class="badge badge-secondary">{{ employee.national_id_number }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <a href="{{ url_for('edit_employee', id=employee.id) }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-edit mr-2"></i>
                    تعديل البيانات
                </a>
                <a href="{{ url_for('employees_list') }}" class="btn btn-secondary btn-lg mr-3">
                    <i class="fas fa-arrow-right mr-2"></i>
                    العودة للقائمة
                </a>
                <button class="btn btn-info btn-lg mr-3" onclick="printProfile()">
                    <i class="fas fa-print mr-2"></i>
                    طباعة الملف
                </button>
                <button class="btn btn-warning btn-lg mr-3" onclick="exportProfile()">
                    <i class="fas fa-file-pdf mr-2"></i>
                    تصدير PDF
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function printProfile() {
    window.print();
}

function exportProfile() {
    alert('سيتم تصدير الملف إلى PDF...');
}

$(document).ready(function() {
    // تأثيرات بصرية
    $('.card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );
});
</script>

<style>
@media print {
    .btn, .card-header, .breadcrumb {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}

.badge-lg {
    font-size: 1rem;
    padding: 0.5rem 1rem;
}
</style>
{% endblock %}
